<template>
  <div class="scheduled-publish" :class="{ 'sidebar-collapsed': isCollapse }">
    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content">
      <!-- 左侧表单区域 -->
      <div class="form-section">
        <el-card class="form-card">
          <template #header>
            <div class="form-header">
              <el-icon class="header-icon"><Timer /></el-icon>
              <span>定时任务</span>
            </div>
          </template>
                
          <el-form 
            ref="scheduleFormRef" 
            :model="scheduleForm" 
            :rules="scheduleRules" 
            label-width="100px"
            class="generate-form"
          >
            <div class="form-content">
              <!-- 任务计划名称 -->
              <el-form-item label="计划名称" prop="plan_name">
                <el-input 
                  v-model="scheduleForm.plan_name" 
                  placeholder="输入任务计划名称"
                  class="keyword-input"
                />
              </el-form-item>

              <!-- 关键词配置 -->
              <el-form-item label="关键词来源">
                <el-radio-group v-model="scheduleForm.keyword_source" @change="onKeywordSourceChange">
                  <el-radio value="custom">自定义关键词</el-radio>
                  <el-radio value="library">从词库选择</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item 
                v-if="scheduleForm.keyword_source === 'custom'"
                label="关键词" 
                prop="custom_keywords"
              >
                <el-input 
                  v-model="scheduleForm.custom_keywords" 
                  placeholder="输入关键词，逗号分隔"
                  :rows="3"
                  type="textarea"
                  resize="none"
                  class="keyword-input"
                />
              </el-form-item>

              <el-form-item
                v-if="scheduleForm.keyword_source === 'library'"
                label="词库分类"
                prop="keyword_categories"
              >
                <el-select
                  v-model="scheduleForm.keyword_categories"
                  placeholder="选择词库分类"
                  multiple
                  style="width: 100%;"
                  @change="onKeywordCategoriesChange"
                >
                  <el-option
                    v-for="category in keywordCategories"
                    :key="category"
                    :label="category"
                    :value="category"
                  />
                </el-select>
              </el-form-item>

              <!-- 智能选词策略按钮 -->
              <el-form-item
                v-if="scheduleForm.keyword_source === 'library'"
                label="智能筛选"
              >
                <el-button
                  type="primary"
                  @click="openStrategyDialog"
                  style="width: 100%;"
                >
                  <el-icon><MagicStick /></el-icon>
                  配置智能选词策略
                </el-button>
                <div class="form-tip" v-if="hasStrategyConfig">
                  <el-icon><Check /></el-icon>
                  已配置智能策略：将根据条件从选定分类中筛选关键词
                </div>
              </el-form-item>

              <!-- 智能推荐 -->
              <el-form-item
                v-if="scheduleForm.keyword_source === 'library'"
                label="智能推荐"
              >
                <el-radio-group v-model="scheduleForm.keyword_selection_strategy" style="width: 100%;">
                  <el-radio value="random_one" style="width: 100%; margin-bottom: 8px;">
                    <div class="strategy-option">
                      <div class="strategy-title">随机1个</div>
                      <div class="strategy-desc">每次任务执行时从筛选范围内随机选择1个关键词</div>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>



              <!-- 站点配置 -->
              <el-form-item label="站点选择" prop="selected_sites">
                <el-select
                  v-model="scheduleForm.selected_sites"
                  placeholder="请选择站点（支持树形选择）"
                  multiple
                  filterable
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  style="width: 100%;"
                  @visible-change="onSiteSelectVisibleChange"
                  @clear="onSiteSelectClear"
                  popper-class="site-select-dropdown"
                >
                  <!-- 隐藏的选项，用于显示选中值 -->
                  <el-option
                    v-for="option in siteOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    style="display: none;"
                  />

                  <!-- 树形选择器 -->
                  <div class="site-tree-container">
                    <div class="tree-header">
                      <span class="tree-title">选择站点（按分类组织）</span>
                    </div>
                    <el-tree
                      ref="siteTreeRef"
                      :data="siteTreeData"
                      show-checkbox
                      node-key="id"
                      :props="siteTreeProps"
                      @check="onSiteTreeCheck"
                      :default-expand-all="false"
                      :expand-on-click-node="false"
                      :check-on-click-node="true"
                      class="site-tree-dropdown"
                      :filter-node-method="filterSiteNode"
                    >
                      <template #default="{ data }">
                        <div class="tree-node">
                          <div class="node-content">
                            <el-icon v-if="data.type === 'category'" class="node-icon">
                              <Folder />
                            </el-icon>
                            <el-icon v-else class="node-icon">
                              <Globe />
                            </el-icon>
                            <span class="node-label">{{ data.label }}</span>
                            <span v-if="data.type === 'category'" class="node-count">({{ data.children?.length || 0 }}个站点)</span>
                          </div>
                          <div v-if="data.type === 'site'" class="node-details">
                            <span class="site-url">{{ data.url }}</span>
                          </div>
                        </div>
                      </template>
                    </el-tree>
                  </div>
                </el-select>

                <!-- 选择结果显示 -->
                <div v-if="getSelectedSiteCount() > 0" class="selection-hint">
                  已选择站点：{{ getSelectedSiteCount() }} 个
                </div>
              </el-form-item>

              <!-- AI模型配置 - 暂时隐藏 -->
              <el-form-item label="AI模型" v-if="false">
                <el-select v-model="scheduleForm.ai_model" placeholder="选择模型（可选）" style="width: 100%;">
                  <el-option label="不指定" value="" />
                  <el-option label="WanX" value="WanX" />
                  <el-option label="Jimeng" value="Jimeng" />
                </el-select>
              </el-form-item>

              <!-- AI配置选择 - 暂时隐藏 -->
              <el-form-item label="AI配置" v-if="false">
                <el-select v-model="scheduleForm.ai_config_id" placeholder="选择配置（可选）" style="width: 100%;">
                  <el-option label="使用默认配置" :value="null" />
                  <el-option
                    v-for="config in aiConfigs"
                    :key="config.id"
                    :label="config.name"
                    :value="config.id"
                    :disabled="!config.is_active"
                  />
                </el-select>
              </el-form-item>

              <!-- 默认博客标签配置 -->
              <el-form-item label="默认标签">
                <el-select 
                  v-model="scheduleForm.default_blog_tags" 
                  placeholder="可选择通用标签（无站点限制）" 
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  clearable
                  style="width: 100%;"
                  :max-collapse-tags="2"
                >
                  <el-option 
                    v-for="tag in commonTags" 
                    :key="tag.id" 
                    :label="tag.name" 
                    :value="tag.id"
                  >
                    <div class="tag-option">
                      <div class="tag-name">{{ tag.name }}</div>
                      <div class="tag-info">
                        <span class="tag-count">{{ tag.count }}篇文章</span>
                        <span v-if="tag.description" class="tag-desc">{{ tag.description }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
                <div class="form-tip">
                  设置的默认标签会应用到所有站点，站点配置中可以单独调整
                </div>
              </el-form-item>

              <!-- 定时配置 -->
              <el-form-item label="计划有效期" prop="scheduled_time">
                <el-row :gutter="12">
                  <el-col :span="12">
                    <el-date-picker
                      v-model="scheduleForm.scheduled_time"
                      type="datetime"
                      placeholder="开始时间"
                      style="width: 100%;"
                      :disabled-date="disabledDate"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-date-picker
                      v-model="scheduleForm.end_time"
                      type="datetime"
                      placeholder="结束时间（可选）"
                      style="width: 100%;"
                      :disabled-date="disabledEndDate"
                    />
                  </el-col>
                </el-row>
              </el-form-item>

              <!-- 发布频率配置 -->
              <el-form-item label="发布频率" prop="frequency_type">
                <el-radio-group v-model="scheduleForm.frequency_type" @change="onFrequencyTypeChange">
                  <el-radio value="once">仅执行一次</el-radio>
                  <el-radio value="daily">每天</el-radio>
                  <el-radio value="weekly">每周</el-radio>
                  <el-radio value="custom">自定义间隔</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 每周执行日期配置 -->
              <el-form-item 
                v-if="scheduleForm.frequency_type === 'weekly'"
                label="执行日期"
              >
                <el-checkbox-group v-model="scheduleForm.weekly_days">
                  <el-checkbox value="1">周一</el-checkbox>
                  <el-checkbox value="2">周二</el-checkbox>
                  <el-checkbox value="3">周三</el-checkbox>
                  <el-checkbox value="4">周四</el-checkbox>
                  <el-checkbox value="5">周五</el-checkbox>
                  <el-checkbox value="6">周六</el-checkbox>
                  <el-checkbox value="0">周日</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <!-- 自定义间隔配置 -->
              <el-form-item 
                v-if="scheduleForm.frequency_type === 'custom'"
                label="间隔设置" 
                prop="custom_interval_value"
              >
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="scheduleForm.custom_interval_value"
                      :min="1"
                      :max="365"
                      placeholder="间隔数值"
                      style="width: 100%;"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-select 
                      v-model="scheduleForm.custom_interval_unit" 
                      placeholder="时间单位"
                      style="width: 100%;"
                    >
                      <el-option label="小时" value="hours" />
                      <el-option label="天" value="days" />
                      <el-option label="周" value="weeks" />
                    </el-select>
                  </el-col>
                </el-row>
              </el-form-item>

              <!-- 每日执行时间配置 -->
              <el-form-item 
                v-if="scheduleForm.frequency_type === 'daily'"
                label="执行时间"
              >
                <el-time-picker
                  v-model="scheduleForm.daily_time"
                  placeholder="选择每日执行时间"
                  style="width: 100%;"
                  format="HH:mm"
                  value-format="HH:mm"
                />
              </el-form-item>

              <!-- 最大执行次数 -->
              <el-form-item 
                v-if="scheduleForm.frequency_type !== 'once'"
                label="最大执行次数"
              >
                <el-input-number
                  v-model="scheduleForm.max_executions"
                  :min="1"
                  placeholder="留空表示无限制"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>

            <!-- 固定按钮区域 -->
            <div class="form-buttons">
              <el-button 
                type="primary" 
                @click="generateSiteConfigs" 
                :loading="generating"
                :disabled="!isFormValid"
                class="form-button"
              >
                <el-icon><MagicStick /></el-icon>
                生成站点配置
              </el-button>
              <el-button @click="resetForm" class="form-button">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 右侧任务管理区域 -->
      <div class="list-section">
        <el-card class="list-card">
          <template #header>
            <div class="content-header">
              <div class="header-row">
                <div class="list-header">
                  <el-icon class="header-icon"><DocumentCopy /></el-icon>
                  <span>发布任务管理</span>
                </div>

                <!-- 队列状态统计栏 -->
                <div class="header-stats">
                  <div class="stat-item">
                    <div class="stat-number">{{ queueStatus.total_queued || 0 }}</div>
                    <div class="stat-label">排队任务</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ queueStatus.running_tasks || 0 }}</div>
                    <div class="stat-label">执行中</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ queueStatus.available_workers || 5 }}</div>
                    <div class="stat-label">可用工作者</div>
                  </div>
                  <div v-if="queueStatus.estimated_wait_time" class="stat-item">
                    <div class="stat-number">{{ formatWaitTime(queueStatus.estimated_wait_time) }}</div>
                    <div class="stat-label">预估等待</div>
                  </div>
                </div>

                <div class="search-filters">
                  <el-input
                    v-model="searchKeywords"
                    placeholder="搜索计划名称"
                    style="width: 200px; margin-right: 10px;"
                    @input="handleSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px;" @change="loadPlans">
                    <el-option label="全部" value="" />
                    <el-option label="启用" value="active" />
                    <el-option label="禁用" value="inactive" />
                  </el-select>
                  <el-button
                    type="primary"
                    size="small"
                    @click="refreshAllData"
                    :loading="refreshing"
                  >
                    <el-icon><Refresh /></el-icon>
                    {{ refreshing ? '刷新中...' : '刷新数据' }}
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="cleanupQueue"
                    :loading="cleaning"
                  >
                    清理队列
                  </el-button>
                </div>
              </div>
            </div>
          </template>

          <div class="list-content">
            <el-table
              :data="planList"
              v-loading="loading"
              row-key="id"
              class="plan-table"
              height="calc(100vh - 350px)"
              :scrollbar-always-on="true"
            >
              <el-table-column prop="plan_name" label="计划名称" width="100" show-overflow-tooltip />
              <el-table-column prop="created_at" label="创建时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="站点配置" width="160">
                <template #default="scope">
                  <div>
                    <div v-if="scope.row.site_categories && getSiteCategories(scope.row.site_categories).length > 0">
                      按分类：{{ getSiteCategories(scope.row.site_categories).join('、') }}
                    </div>
                    <div v-else-if="scope.row.selected_sites && getSiteCount(scope.row.selected_sites) > 0">
                      自定义：{{ getSiteCount(scope.row.selected_sites) }} 个站点
                    </div>
                    <div v-else class="text-muted">未配置</div>
                    <div class="site-count-info" style="font-size: 12px; color: #666; margin-top: 2px;">
                      站点数量：{{ getTotalSiteCount(scope.row) }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="发布频率" width="100">
                <template #default="scope">
                  <el-tag size="small" :type="getFrequencyTagType(scope.row.frequency_type)">
                    {{ getFrequencyText(scope.row.frequency_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="last_execution_time" label="上次执行" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.last_execution_time) }}
                </template>
              </el-table-column>
              <el-table-column label="执行统计" width="100" align="center">
                <template #default="scope">
                  <span class="task-stats">{{ scope.row.success_tasks }}/{{ scope.row.total_tasks }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="is_active" label="状态" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.is_active ? 'success' : 'info'" size="small">
                    {{ scope.row.is_active ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="scheduled_time" label="开始时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.scheduled_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="end_time" label="结束时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.end_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140" fixed="right" align="center">
                <template #default="scope">
                  <div class="action-buttons">
                    <div class="button-row">
                      <el-button type="primary" size="small" @click="viewPlanTasks(scope.row)">
                        明细
                      </el-button>
                      <el-button type="success" size="small" @click="executePlanNow(scope.row)" :disabled="!scope.row.is_active">
                        执行
                      </el-button>
                    </div>
                    <div class="button-row">
                      <el-button type="warning" size="small" @click="editPlan(scope.row)">
                        编辑
                      </el-button>
                      <el-button type="danger" size="small" @click="deletePlan(scope.row)">
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 站点配置验证对话框 -->
    <el-drawer
      v-model="configDialogVisible"
      title="站点配置验证与提交"
      direction="rtl"
      size="60%"
      :before-close="handleConfigDialogClose"
    >
      <div class="config-dialog-content">
        <div class="config-summary">
          <el-alert
            :title="`共生成 ${siteConfigs.length} 个站点配置，请验证并选择要执行的配置`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>

        <div class="config-form">
          <el-form :model="scheduleForm" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划名称">
                  <el-input v-model="scheduleForm.plan_name" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划开始时间">
                  <el-date-picker
                    v-model="scheduleForm.scheduled_time"
                    type="datetime"
                    placeholder="选择开始时间"
                    style="width: 100%;"
                    :disabled-date="disabledDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划结束时间">
                  <el-date-picker
                    v-model="scheduleForm.end_time"
                    type="datetime"
                    placeholder="选择结束时间（可选）"
                    style="width: 100%;"
                    :disabled-date="disabledEndDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发布频率">
                  <el-select v-model="scheduleForm.frequency_type" style="width: 100%;" disabled>
                    <el-option label="仅执行一次" value="once" />
                    <el-option label="每天" value="daily" />
                    <el-option label="每周" value="weekly" />
                    <el-option label="自定义间隔" value="custom" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="scheduleForm.frequency_type !== 'once'" :gutter="20">
              <el-col :span="24">
                <el-form-item label="频率详情">
                  <div class="frequency-details">
                    <span v-if="scheduleForm.frequency_type === 'daily'">
                      每天 {{ scheduleForm.daily_time || '未设置时间' }} 执行
                    </span>
                    <span v-else-if="scheduleForm.frequency_type === 'weekly'">
                      每周 {{ getWeeklyDaysText() }} 执行
                    </span>
                    <span v-else-if="scheduleForm.frequency_type === 'custom'">
                      每 {{ scheduleForm.custom_interval_value }} {{ getIntervalUnitText() }} 执行一次
                    </span>
                    <span v-if="scheduleForm.max_executions" class="max-executions">
                      （最多执行 {{ scheduleForm.max_executions }} 次）
                    </span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="config-list">
          <div class="config-header">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              :indeterminate="isIndeterminate"
            >
              全选站点配置
            </el-checkbox>
            <span class="selected-count">
              已选择 {{ selectedConfigs.length }} / {{ siteConfigs.length }} 个配置
            </span>
          </div>

          <!-- 表格形式的站点配置列表 -->
          <div class="config-table-container">
            <el-table 
              ref="configTableRef"
              :data="siteConfigs" 
              class="config-table"
              row-key="site_id"
              size="small"
              @selection-change="handleTableSelectionChange"
              max-height="400"
            >
              <!-- 选择列 -->
              <el-table-column type="selection" width="55" />
              
              <!-- 站点信息 -->
              <el-table-column label="站点信息" min-width="200">
                <template #default="scope">
                  <div class="site-info-compact">
                    <div class="site-name">{{ scope.row.site_name }}</div>
                    <div class="site-url">{{ scope.row.site_url }}</div>
                  </div>
                </template>
              </el-table-column>
              
              <!-- 关键词 -->
              <el-table-column label="关键词" min-width="250">
                <template #default="scope">
                  <div class="keywords-compact">
                    <el-tag 
                      v-for="(keyword, kidx) in scope.row.keywords" 
                      :key="kidx"
                      size="small"
                      class="keyword-tag-small"
                      closable
                      @close="removeKeyword(scope.$index, kidx)"
                    >
                      {{ keyword }}
                    </el-tag>
                    <el-button 
                      size="small" 
                      type="primary" 
                      link 
                      @click="showAddKeywordDialog(scope.$index)"
                      style="margin-left: 4px;"
                    >
                      <el-icon><Plus /></el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              
              <!-- 文章分类 -->
              <el-table-column label="文章分类" width="180">
                <template #default="scope">
                  <el-select 
                    v-model="scope.row.blog_category_id" 
                    placeholder="选择分类"
                    size="small"
                    style="width: 100%;"
                  >
                    <el-option 
                      v-for="category in scope.row.blog_categories || []" 
                      :key="category.id" 
                      :label="category.name" 
                      :value="category.id"
                    />
                  </el-select>
                </template>
              </el-table-column>

              <!-- 博客标签 -->
              <el-table-column label="博客标签" width="200">
                <template #default="scope">
                  <el-select 
                    v-model="scope.row.blog_tags" 
                    placeholder="选择现有标签或输入新标签名称"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    size="small"
                    style="width: 100%;"
                    :max-collapse-tags="1"
                    clearable
                    filterable
                    allow-create
                    default-first-option
                    :filter-method="(query) => filterSiteConfigTags(scope.$index, query)"
                    :reserve-keyword="false"
                  >
                    <el-option 
                      v-for="tag in scope.row.filtered_blog_tags_options || []" 
                      :key="tag.id" 
                      :label="tag.name" 
                      :value="tag.id"
                    >
                      <div style="display: flex; justify-content: space-between;">
                        <span>{{ tag.name }}</span>
                        <span style="color: #999; font-size: 12px;">{{ tag.count || 0 }}篇</span>
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="dialog-actions">
          <el-button @click="configDialogVisible = false">
            取消
          </el-button>
          <el-button @click="resetConfigs">
            重新生成
          </el-button>
          <el-button 
            type="primary" 
            @click="submitConfigs"
            :loading="submitting"
            :disabled="selectedConfigs.length === 0"
          >
            <el-icon><Check /></el-icon>
            {{ submitting ? '提交中...' : `提交 ${selectedConfigs.length} 个配置` }}
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 添加关键词对话框 -->
    <el-dialog
      v-model="addKeywordDialogVisible"
      title="添加关键词"
      width="500px"
    >
      <el-form :model="addKeywordForm" label-width="100px">
        <el-form-item label="关键词">
          <el-input 
            v-model="addKeywordForm.keyword" 
            placeholder="输入要添加的关键词"
            @keyup.enter="addKeyword"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addKeywordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addKeyword">添加</el-button>
      </template>
    </el-dialog>

    <!-- 智能选词策略对话框 -->
    <el-dialog
      v-model="strategyDialogVisible"
      title="智能选词策略配置"
      width="80%"
      :before-close="handleStrategyDialogClose"
      append-to-body
    >
      <div class="strategy-dialog-content">
        <!-- 左侧：策略配置 -->
        <div class="strategy-config-panel">
          <el-card shadow="never" class="config-card">
            <template #header>
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>策略配置</span>
              </div>
            </template>

            <el-form :model="strategyForm" label-width="120px" size="default">
              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="关键词意图类型：信息型（寻找信息）、商业型（比较产品）、导航型（寻找特定网站）、交易型（准备购买）"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      关键词意图
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="strategyForm.strategy_intent"
                  placeholder="选择关键词意图（可选）"
                  clearable
                  style="width: 100%;"
                  @change="onStrategyChange"
                >
                  <el-option label="信息型 (Informational)" value="Informational" />
                  <el-option label="商业型 (Commercial)" value="Commercial" />
                  <el-option label="导航型 (Navigational)" value="Navigational" />
                  <el-option label="交易型 (Transactional)" value="Transactional" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="关键词在过去12个月的平均月搜索量"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      搜索量范围
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_volume_min"
                      placeholder="最小搜索量"
                      :min="0"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_volume_max"
                      placeholder="最大搜索量"
                      :min="0"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="关键词难度（KD%）是衡量内容在Google前10名有机排名所需SEO努力的指标。分数从0到100，百分比越高，Semrush预测排名越困难。0-14=非常容易，15-29=容易，30-49=可能，50-69=困难，70-84=很难，85-100=非常困难。"
                    placement="top"
                    effect="dark"
                    :show-after="200"
                    popper-class="wide-tooltip"
                  >
                    <span class="label-with-help">
                      关键词难度
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-slider
                  v-model="strategyForm.difficulty_range"
                  range
                  :min="1"
                  :max="100"
                  :step="1"
                  show-stops
                  show-input
                  @change="onDifficultyRangeChange"
                  @input="onStrategyChange"
                />
                <div class="range-display">
                  难度范围：{{ strategyForm.difficulty_range[0] }} - {{ strategyForm.difficulty_range[1] }}
                </div>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="每次点击费用：广告主为用户点击由所列关键词触发的广告而支付的平均价格（美元）"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      CPC范围(USD)
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_cpc_min"
                      placeholder="最小CPC"
                      :min="0"
                      :precision="2"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_cpc_max"
                      placeholder="最大CPC"
                      :min="0"
                      :precision="2"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="广告主在其PPC广告系列中针对所列关键词出价的竞争程度。竞争密度以0到1.00的等级显示，1.00表示排名最困难。"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      竞争密度
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-slider
                  v-model="strategyForm.competitive_density_range"
                  range
                  :min="0"
                  :max="1"
                  :step="0.01"
                  show-input
                  @change="onCompetitiveDensityRangeChange"
                  @input="onStrategyChange"
                />
                <div class="range-display">
                  竞争密度范围：{{ strategyForm.competitive_density_range[0].toFixed(2) }} - {{ strategyForm.competitive_density_range[1].toFixed(2) }}
                </div>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="12个月内搜索者对特定关键词的兴趣。该指标根据每月查询量的变化而定"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      趋势变化
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="strategyForm.strategy_trend_direction"
                  placeholder="选择趋势方向（可选）"
                  clearable
                  style="width: 100%;"
                  @change="onStrategyChange"
                >
                  <el-option label="上升趋势" value="up" />
                  <el-option label="下降趋势" value="down" />
                  <el-option label="平稳趋势" value="stable" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <template #label>
                  <el-tooltip
                    content="特定关键词在自然搜索结果中显示的URL数量"
                    placement="top"
                    effect="dark"
                  >
                    <span class="label-with-help">
                      搜索结果数范围
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </span>
                  </el-tooltip>
                </template>
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_results_min"
                      placeholder="最小结果数"
                      :min="0"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="strategyForm.strategy_results_max"
                      placeholder="最大结果数"
                      :min="0"
                      style="width: 100%;"
                      @change="onStrategyChange"
                    />
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item label="目标国家">
                <el-select
                  v-model="strategyForm.strategy_countries"
                  placeholder="选择目标国家（可选）"
                  multiple
                  clearable
                  style="width: 100%;"
                  @change="onStrategyChange"
                >
                  <el-option label="全球" value="global" />
                  <el-option label="美国" value="US" />
                  <el-option label="英国" value="GB" />
                  <el-option label="加拿大" value="CA" />
                  <el-option label="澳大利亚" value="AU" />
                  <el-option label="德国" value="DE" />
                  <el-option label="法国" value="FR" />
                  <el-option label="日本" value="JP" />
                  <el-option label="中国" value="CN" />
                </el-select>
              </el-form-item>

              <div class="strategy-actions">
                <el-button @click="clearStrategyForm" style="width: 48%;">
                  <el-icon><Delete /></el-icon>
                  清空配置
                </el-button>
                <el-button type="primary" @click="refreshPreview" :loading="previewLoading" style="width: 48%;">
                  <el-icon><Refresh /></el-icon>
                  刷新预览
                </el-button>
              </div>
            </el-form>
          </el-card>
        </div>

        <!-- 右侧：策略预览 -->
        <div class="strategy-preview-panel">
          <el-card shadow="never" class="preview-card">
            <template #header>
              <div class="card-header">
                <el-icon><View /></el-icon>
                <span>预览结果</span>
                <el-tag v-if="strategyPreviewData.total_count > 0" type="success" size="small">
                  {{ strategyPreviewData.total_count }} 个关键词
                </el-tag>
              </div>
            </template>

            <div class="preview-content" v-loading="previewLoading">
              <el-empty v-if="!strategyPreviewData.preview_keywords.length && !previewLoading"
                description="暂无符合条件的关键词" />

              <div v-else class="preview-table-container">
                <el-table
                  :data="strategyPreviewData.preview_keywords"
                  style="width: 100%"
                  height="350"
                  size="small"
                >
                  <el-table-column prop="keyword_name" label="关键词" width="180" show-overflow-tooltip />
                  <el-table-column prop="intent" label="意图" width="100" />
                  <el-table-column prop="volume" label="搜索量" width="90" align="right">
                    <template #default="scope">
                      {{ scope.row.volume ? scope.row.volume.toLocaleString() : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="keyword_difficulty" label="难度" width="70" align="center">
                    <template #default="scope">
                      {{ scope.row.keyword_difficulty || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="cpc_usd" label="CPC" width="80" align="right">
                    <template #default="scope">
                      {{ scope.row.cpc_usd ? '$' + scope.row.cpc_usd.toFixed(2) : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="competitive_density" label="竞争密度" width="90" align="center">
                    <template #default="scope">
                      {{ scope.row.competitive_density ? scope.row.competitive_density.toFixed(2) : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="trend" label="趋势" width="120" align="center">
                    <template #default="scope">
                      <div v-if="scope.row.trend" class="trend-display-preview">
                        <div class="trend-chart-simple-preview" :class="getTrendDirectionPreview(scope.row.trend)">
                          <div
                            v-for="(point, index) in getTrendPointsPreview(scope.row.trend)"
                            :key="index"
                            class="trend-point-preview"
                            :style="{ height: point + '%' }"
                          ></div>
                        </div>
                        <span class="trend-text-preview">{{ getTrendSummaryPreview(scope.row.trend) }}</span>
                      </div>
                      <span v-else class="text-muted">--</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="number_of_results" label="搜索结果数" width="110" align="right">
                    <template #default="scope">
                      <span v-if="scope.row.number_of_results">{{ formatNumberPreview(scope.row.number_of_results) }}</span>
                      <span v-else class="text-muted">--</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="category" label="分类" show-overflow-tooltip />
                  <el-table-column label="国家/地区" width="120" show-overflow-tooltip>
                    <template #default="scope">
                      {{ scope.row.location_ids || '全球' }}
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="preview-pagination">
                  <el-pagination
                    v-model:current-page="previewCurrentPage"
                    v-model:page-size="previewPageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="previewTotal"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handlePreviewSizeChange"
                    @current-change="handlePreviewCurrentChange"
                    small
                  />
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="strategyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyStrategy" :disabled="strategyPreviewData.total_count === 0">
            应用策略
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="taskDetailVisible"
      :title="`${selectedPlan?.plan_name} - 任务详情`"
      width="80%"
      append-to-body
    >
      <el-table
        :data="taskList"
        v-loading="taskLoading"
        class="task-table"
        :scrollbar-always-on="false"
      >
        <el-table-column prop="site_name" label="站点名称" width="120" show-overflow-tooltip />
        <el-table-column label="选词策略" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ getKeywordStrategyText(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="具体关键词" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ getActualKeyword(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="文章标题" width="200" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.title" class="article-title">{{ scope.row.title }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="blog_category_name" label="文章分类" width="100" show-overflow-tooltip />
        <el-table-column prop="blog_tags" label="博客标签" width="120" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.blog_tags" class="tags-display">
              <el-tag
                v-for="tag in parseTagsDisplay(scope.row.blog_tags)"
                :key="tag"
                size="small"
                type="info"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="execution_time" label="执行时间" width="140" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatDateTime(row.execution_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="completion_time" label="完成时间" width="140" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatDateTime(row.completion_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.ai_article_id && scope.row.article_url"
              type="primary"
              size="small"
              @click="viewArticle(scope.row.ai_article_id)"
            >
              查看
            </el-button>
            <el-button
              v-else-if="scope.row.ai_article_id && !scope.row.article_url"
              type="info"
              size="small"
              disabled
            >
              未发布
            </el-button>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="taskCurrentPage"
          v-model:page-size="taskPageSize"
          :page-sizes="[10, 20, 50]"
          :total="taskTotal"
          layout="total, sizes, prev, pager, next"
          @size-change="handleTaskSizeChange"
          @current-change="handleTaskCurrentChange"
        />
      </div>
    </el-dialog>

    <!-- 编辑计划对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑定时任务计划"
      width="1200px"
      :before-close="handleEditDialogClose"
    >
      <div class="edit-dialog-content">
        <!-- 左侧：通用配置 -->
        <div class="edit-left-panel">
          <div class="panel-header">
            <el-icon><Setting /></el-icon>
            <span>通用配置</span>
          </div>

          <el-form
            ref="editFormRef"
            :model="editForm"
            :rules="editRules"
            label-width="120px"
            size="default"
            class="edit-form"
          >
        <el-form-item label="计划名称" prop="plan_name">
          <el-input
            v-model="editForm.plan_name"
            placeholder="请输入计划名称"
          />
        </el-form-item>

        <el-form-item label="关键词来源">
          <el-radio-group v-model="editForm.keyword_source" @change="onEditKeywordSourceChange">
            <el-radio value="custom">自定义关键词</el-radio>
            <el-radio value="library">从词库选择</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="editForm.keyword_source === 'custom'"
          label="关键词"
          prop="custom_keywords"
        >
          <el-input
            v-model="editForm.custom_keywords"
            placeholder="输入关键词，逗号分隔"
            :rows="3"
            type="textarea"
            resize="none"
          />
        </el-form-item>

        <el-form-item
          v-if="editForm.keyword_source === 'library'"
          label="词库分类"
          prop="keyword_categories"
        >
          <el-select
            v-model="editForm.keyword_categories"
            placeholder="选择词库分类"
            multiple
            style="width: 100%;"
          >
            <el-option
              v-for="category in keywordCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <!-- 编辑对话框中的智能筛选 -->
        <el-form-item
          v-if="editForm.keyword_source === 'library'"
          label="智能筛选"
        >
          <el-button
            type="primary"
            @click="openEditStrategyDialog"
            style="width: 100%;"
          >
            <el-icon><MagicStick /></el-icon>
            配置智能选词策略
          </el-button>
          <div class="form-tip" v-if="editForm.use_keyword_strategy">
            <el-icon><Check /></el-icon>
            已配置智能策略：将根据条件从选定分类中筛选关键词
          </div>
        </el-form-item>

        <!-- 编辑对话框中的智能推荐 -->
        <el-form-item
          v-if="editForm.keyword_source === 'library'"
          label="智能推荐"
        >
          <el-radio-group v-model="editForm.keyword_selection_strategy" style="width: 100%;">
            <el-radio value="random_one" style="width: 100%; margin-bottom: 8px;">
              <div class="strategy-option">
                <div class="strategy-title">随机1个</div>
                <div class="strategy-desc">每次任务执行时从筛选范围内随机选择1个关键词</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="站点选择" prop="selected_sites">
          <el-select
            v-model="editForm.selected_sites"
            placeholder="请选择站点（支持树形选择）"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            style="width: 100%;"
            @visible-change="onEditSiteSelectVisibleChange"
            @clear="onEditSiteSelectClear"
            popper-class="site-select-dropdown"
          >
            <!-- 隐藏的选项，用于显示选中值 -->
            <el-option
              v-for="option in siteOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              style="display: none;"
            />

            <!-- 树形选择器 -->
            <div class="site-tree-container">
              <div class="tree-header">
                <span class="tree-title">选择站点（按分类组织）</span>
              </div>
              <el-tree
                ref="editSiteTreeRef"
                :data="siteTreeData"
                show-checkbox
                node-key="id"
                :props="siteTreeProps"
                @check="onEditSiteTreeCheck"
                :default-expand-all="false"
                :expand-on-click-node="false"
                :check-on-click-node="true"
                class="site-tree-dropdown"
                :filter-node-method="filterSiteNode"
              >
                <template #default="{ data }">
                  <div class="tree-node">
                    <div class="node-content">
                      <el-icon v-if="data.type === 'category'" class="node-icon">
                        <Folder />
                      </el-icon>
                      <el-icon v-else class="node-icon">
                        <Globe />
                      </el-icon>
                      <span class="node-label">{{ data.label }}</span>
                      <span v-if="data.type === 'category'" class="node-count">({{ data.children?.length || 0 }}个站点)</span>
                    </div>
                    <div v-if="data.type === 'site'" class="node-details">
                      <span class="site-url">{{ data.url }}</span>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </el-select>

          <!-- 选择结果显示 -->
          <div v-if="getEditSelectedSiteCount() > 0" class="selection-hint">
            已选择站点：{{ getEditSelectedSiteCount() }} 个
          </div>
        </el-form-item>

        <el-form-item label="发布频率" prop="frequency_type">
          <el-select v-model="editForm.frequency_type" style="width: 100%;" @change="onEditFrequencyChange">
            <el-option label="仅执行一次" value="once" />
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="自定义间隔" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="editForm.frequency_type === 'weekly'"
          label="执行日期"
          prop="weekly_days"
        >
          <el-checkbox-group v-model="editForm.weekly_days">
            <el-checkbox value="0">周日</el-checkbox>
            <el-checkbox value="1">周一</el-checkbox>
            <el-checkbox value="2">周二</el-checkbox>
            <el-checkbox value="3">周三</el-checkbox>
            <el-checkbox value="4">周四</el-checkbox>
            <el-checkbox value="5">周五</el-checkbox>
            <el-checkbox value="6">周六</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item
          v-if="editForm.frequency_type === 'custom'"
          label="间隔设置"
        >
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input-number
                v-model="editForm.custom_interval_value"
                :min="1"
                :max="365"
                style="width: 100%;"
              />
            </el-col>
            <el-col :span="12">
              <el-select v-model="editForm.custom_interval_unit" style="width: 100%;">
                <el-option label="小时" value="hours" />
                <el-option label="天" value="days" />
                <el-option label="周" value="weeks" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item
          v-if="editForm.frequency_type === 'daily'"
          label="每日执行时间"
        >
          <el-time-picker
            v-model="editForm.daily_time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="执行时间">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-date-picker
                v-model="editForm.scheduled_time"
                type="datetime"
                placeholder="开始时间"
                style="width: 100%;"
                :disabled-date="disabledDate"
              />
            </el-col>
            <el-col :span="12">
              <el-date-picker
                v-model="editForm.end_time"
                type="datetime"
                placeholder="结束时间（可选）"
                style="width: 100%;"
                :disabled-date="disabledEndDate"
              />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item
          v-if="editForm.frequency_type !== 'once'"
          label="最大执行次数"
        >
          <el-input-number
            v-model="editForm.max_executions"
            :min="1"
            :max="1000"
            placeholder="不限制"
            style="width: 100%;"
          />
        </el-form-item>

            <el-form-item label="状态">
              <el-switch
                v-model="editForm.is_active"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 右侧：站点配置 -->
        <div class="edit-right-panel">
          <div class="panel-header">
            <el-icon><Globe /></el-icon>
            <span>站点配置</span>
            <div class="header-actions">
              <el-tag
                v-if="editGenerating"
                type="info"
                size="small"
                style="margin-right: 8px;"
              >
                <el-icon class="is-loading"><Refresh /></el-icon>
                自动刷新中...
              </el-tag>
              <el-button
                type="primary"
                size="small"
                @click="generateEditSiteConfigs"
                :loading="editGenerating"
              >
                <el-icon><MagicStick /></el-icon>
                重新生成配置
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="debugSiteConfigs"
                plain
              >
                <el-icon><BugFilled /></el-icon>
                调试配置
              </el-button>
            </div>
          </div>

          <div v-if="siteConfigs.length === 0" class="empty-config">
            <el-empty description="请先选择站点并生成配置" />
          </div>

          <div v-else class="site-configs-container">
            <div class="config-summary">
              <el-alert
                :title="`共 ${siteConfigs.length} 个站点配置`"
                type="info"
                show-icon
                :closable="false"
              />
            </div>

            <!-- 站点配置表格 -->
            <div class="config-table-container">
              <el-table
                :data="siteConfigs"
                class="edit-config-table"
                row-key="site_id"
                size="small"
                max-height="400"
              >
                <el-table-column prop="site_name" label="站点名称" width="120" show-overflow-tooltip />

                <el-table-column label="关键词" width="150">
                  <template #default="scope">
                    <div class="keywords-display">
                      <el-tag
                        v-for="(keyword, index) in scope.row.keywords.slice(0, 2)"
                        :key="index"
                        size="small"
                        style="margin: 1px;"
                      >
                        {{ keyword }}
                      </el-tag>
                      <span v-if="scope.row.keywords.length > 2" class="more-keywords">
                        等{{ scope.row.keywords.length }}个
                      </span>
                    </div>
                  </template>
                </el-table-column>

                <!-- 文章分类 -->
                <el-table-column label="文章分类" width="180">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.blog_category_id"
                      placeholder="选择分类"
                      size="small"
                      style="width: 100%;"
                      filterable
                    >
                      <el-option
                        v-for="category in scope.row.blog_categories"
                        :key="category.id"
                        :label="category.name"
                        :value="category.id"
                      />
                    </el-select>
                  </template>
                </el-table-column>

                <!-- 博客标签 -->
                <el-table-column label="博客标签" width="200">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.blog_tags"
                      placeholder="选择标签"
                      multiple
                      filterable
                      collapse-tags
                      collapse-tags-tooltip
                      :max-collapse-tags="2"
                      size="small"
                      style="width: 100%;"
                      @visible-change="(visible) => handleEditTagSelectVisibleChange(visible, scope.$index)"
                      @filter-method="(query) => filterEditSiteConfigTags(scope.$index, query)"
                    >
                      <el-option
                        v-for="tag in scope.row.filtered_blog_tags_options || scope.row.blog_tags_options"
                        :key="tag.id"
                        :label="tag.name"
                        :value="tag.id"
                      >
                        <div class="tag-option">
                          <div class="tag-name">{{ tag.name }}</div>
                          <div class="tag-info">
                            <span class="tag-count">{{ tag.count || 0 }}篇</span>
                          </div>
                        </div>
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="editSubmitting">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Timer, Setting, List, MagicStick, Refresh, DocumentCopy, Search, Clock, Plus, Check, View, Delete, Folder, Globe, QuestionFilled, TrendCharts, InfoFilled, BugFilled } from '@element-plus/icons-vue'
import apiClient from '@/utils/request'
import axios from 'axios'

// 导入全局时区处理函数
import { formatDateTime as formatDateTimeWithTimezone, formatTableDateTime, toUTCString, fromUTCString, getSystemTimezoneConfig } from '@/utils/timezone.js'

// 响应式数据
const isCollapse = ref(false)
const generating = ref(false)
const submitting = ref(false)
const loading = ref(false)
const taskLoading = ref(false)
const refreshing = ref(false)
const cleaning = ref(false)
const taskDetailVisible = ref(false)
const configDialogVisible = ref(false)
const addKeywordDialogVisible = ref(false)
const editDialogVisible = ref(false)
const editSubmitting = ref(false)

// 表单数据
const scheduleForm = reactive({
  plan_name: '',
  keyword_source: 'library', // 默认选择从词库选择
  custom_keywords: '',
  keyword_categories: [],
  keyword_selection_strategy: 'random_one', // 智能推荐策略

  // 选词策略配置
  strategy_intent: '',
  strategy_volume_min: null,
  strategy_volume_max: null,
  strategy_difficulty_min: null,
  strategy_difficulty_max: null,
  strategy_cpc_min: null,
  strategy_cpc_max: null,
  strategy_competitive_density_min: null,
  strategy_competitive_density_max: null,
  strategy_countries: [],
  strategy_categories: [],

  selected_sites: [], // 统一的站点选择，支持分类和单个站点
  ai_model: '',
  ai_config_id: null,
  scheduled_time: null,
  end_time: null,
  frequency_type: 'once',
  weekly_days: [],
  custom_interval_value: 1,
  custom_interval_unit: 'days',
  daily_time: null,
  max_executions: null,
  default_blog_tags: []
})

// 表单验证规则
const scheduleRules = {
  plan_name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  custom_keywords: [
    { 
      validator: (rule, value, callback) => {
        if (scheduleForm.keyword_source === 'custom' && !value) {
          callback(new Error('请输入关键词'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  keyword_categories: [
    {
      validator: (rule, value, callback) => {
        if (scheduleForm.keyword_source === 'library' && (!value || value.length === 0)) {
          callback(new Error('请选择词库分类'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  selected_sites: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请选择站点或分类'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  scheduled_time: [
    { required: true, message: '请选择开始时间', trigger: 'blur' }
  ],
  frequency_type: [
    { required: true, message: '请选择发布频率', trigger: 'change' }
  ],
  weekly_days: [
    { 
      validator: (rule, value, callback) => {
        if (scheduleForm.frequency_type === 'weekly' && (!value || value.length === 0)) {
          callback(new Error('请选择执行日期'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  custom_interval_value: [
    { 
      validator: (rule, value, callback) => {
        if (scheduleForm.frequency_type === 'custom' && (!value || value < 1)) {
          callback(new Error('请输入有效的间隔数值'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 基础数据
const keywordCategories = ref([])
const siteCategories = ref([])
const allSites = ref([])
const aiConfigs = ref([])
const siteConfigs = ref([])
const commonTags = ref([])
const siteCategoryCounts = ref({})

// 编辑表单的站点配置
const editSiteConfigs = ref([])
const editGenerating = ref(false)
const editAutoRefreshTimer = ref(null)

// 策略对话框相关
const strategyDialogVisible = ref(false)
const previewLoading = ref(false)
const strategyPreviewData = ref({
  total_count: 0,
  preview_keywords: []
})

// 预览分页相关
const previewCurrentPage = ref(1)
const previewPageSize = ref(20)
const previewTotal = ref(0)

// 策略表单数据
const strategyForm = reactive({
  strategy_intent: '',
  strategy_volume_min: null,
  strategy_volume_max: null,
  difficulty_range: [1, 100], // 滑动条范围
  strategy_difficulty_min: 1,
  strategy_difficulty_max: 100,
  strategy_cpc_min: null,
  strategy_cpc_max: null,
  competitive_density_range: [0.00, 1.00], // 滑动条范围
  strategy_competitive_density_min: 0.00,
  strategy_competitive_density_max: 1.00,
  strategy_trend_direction: '', // 趋势方向
  strategy_results_min: null, // 搜索结果数最小值
  strategy_results_max: null, // 搜索结果数最大值
  strategy_countries: [],
  strategy_categories: [] // 这个会自动设置为当前选择的分类
})

// 列表数据
const planList = ref([])
const taskList = ref([])
const selectedPlan = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const taskCurrentPage = ref(1)
const taskPageSize = ref(10)
const taskTotal = ref(0)

// 搜索和筛选
const searchKeywords = ref('')
const statusFilter = ref('')

// 队列状态
const queueStatus = ref({
  total_queued: 0,
  running_tasks: 0,
  available_workers: 5,
  estimated_wait_time: null
})

// 配置对话框相关
const selectedConfigs = ref([])
const currentConfigIndex = ref(-1)
const addKeywordForm = reactive({
  keyword: ''
})

// 编辑表单数据
const editForm = reactive({
  id: null,
  plan_name: '',
  keyword_source: 'library',
  custom_keywords: '',
  keyword_categories: [],
  keyword_selection_strategy: 'random_one', // 智能推荐策略
  use_keyword_strategy: false, // 是否使用智能策略

  // 选词策略配置
  strategy_intent: '',
  strategy_volume_min: null,
  strategy_volume_max: null,
  strategy_difficulty_min: null,
  strategy_difficulty_max: null,
  strategy_cpc_min: null,
  strategy_cpc_max: null,
  strategy_competitive_density_min: null,
  strategy_competitive_density_max: null,
  strategy_trend_direction: '', // 趋势方向
  strategy_results_min: null, // 搜索结果数最小值
  strategy_results_max: null, // 搜索结果数最大值
  strategy_countries: [],
  strategy_categories: [],

  site_source: 'category',
  site_categories: [],
  selected_sites: [],
  frequency_type: 'once',
  weekly_days: [],
  custom_interval_value: 1,
  custom_interval_unit: 'days',
  daily_time: null,
  scheduled_time: null,
  end_time: null,
  max_executions: null,
  is_active: true
})

// 编辑表单验证规则
const editRules = {
  plan_name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  custom_keywords: [
    {
      validator: (rule, value, callback) => {
        if (editForm.keyword_source === 'custom' && !value) {
          callback(new Error('请输入关键词'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  keyword_categories: [
    {
      validator: (rule, value, callback) => {
        if (editForm.keyword_source === 'library' && (!value || value.length === 0)) {
          callback(new Error('请选择词库分类'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  frequency_type: [
    { required: true, message: '请选择发布频率', trigger: 'change' }
  ],
  weekly_days: [
    {
      validator: (rule, value, callback) => {
        if (editForm.frequency_type === 'weekly' && (!value || value.length === 0)) {
          callback(new Error('请选择执行日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  custom_interval_value: [
    {
      validator: (rule, value, callback) => {
        if (editForm.frequency_type === 'custom' && (!value || value < 1)) {
          callback(new Error('请输入有效的间隔数值'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  selected_sites: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请选择站点或分类'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 表单引用
const scheduleFormRef = ref(null)
const configTableRef = ref(null)
const editFormRef = ref(null)
const siteTreeRef = ref(null)
const editSiteTreeRef = ref(null)

// 树形数据结构
const siteTreeData = ref([])
const siteTreeProps = {
  children: 'children',
  label: 'label'
}

// 站点选项（用于下拉框显示）
const siteOptions = computed(() => {
  const options = []
  allSites.value.forEach(site => {
    options.push({
      label: site.name,
      value: site.id
    })
  })
  return options
})

// 计算属性
const isFormValid = computed(() => {
  return scheduleForm.plan_name &&
         scheduleForm.scheduled_time &&
         ((scheduleForm.keyword_source === 'custom' && scheduleForm.custom_keywords) ||
          (scheduleForm.keyword_source === 'library' && scheduleForm.keyword_categories.length > 0)) &&
         scheduleForm.selected_sites && scheduleForm.selected_sites.length > 0
})

const selectAll = computed({
  get: () => selectedConfigs.value.length === siteConfigs.value.length && siteConfigs.value.length > 0,
  set: (value) => {
    // 这个set方法会被表头的全选复选框调用
    handleSelectAll(value)
  }
})

const isIndeterminate = computed(() => {
  return selectedConfigs.value.length > 0 && selectedConfigs.value.length < siteConfigs.value.length
})

// 判断是否有策略配置
const hasStrategyConfig = computed(() => {
  return scheduleForm.use_keyword_strategy ||
         strategyForm.strategy_intent ||
         strategyForm.strategy_volume_min !== null ||
         strategyForm.strategy_volume_max !== null ||
         strategyForm.strategy_difficulty_min !== 1 ||
         strategyForm.strategy_difficulty_max !== 100 ||
         strategyForm.strategy_cpc_min !== null ||
         strategyForm.strategy_cpc_max !== null ||
         strategyForm.strategy_competitive_density_min !== 0.00 ||
         strategyForm.strategy_competitive_density_max !== 1.00 ||
         strategyForm.strategy_trend_direction ||
         strategyForm.strategy_results_min !== null ||
         strategyForm.strategy_results_max !== null ||
         strategyForm.strategy_countries.length > 0
})

// 判断编辑模式下是否有策略配置
const hasEditStrategyConfig = computed(() => {
  return editForm.use_keyword_strategy ||
         editForm.strategy_intent ||
         editForm.strategy_volume_min !== null ||
         editForm.strategy_volume_max !== null ||
         editForm.strategy_difficulty_min !== null ||
         editForm.strategy_difficulty_max !== null ||
         editForm.strategy_cpc_min !== null ||
         editForm.strategy_cpc_max !== null ||
         editForm.strategy_competitive_density_min !== null ||
         editForm.strategy_competitive_density_max !== null ||
         editForm.strategy_trend_direction ||
         editForm.strategy_results_min !== null ||
         editForm.strategy_results_max !== null ||
         (editForm.strategy_countries && editForm.strategy_countries.length > 0)
})

// 手动刷新管理（移除了文件监听机制）

// 初始化SSE连接
const initSSEConnections = () => {
  console.log('🚀 开始初始化SSE连接...')
  // 获取认证token
  const token = localStorage.getItem('token')
  if (!token) {
    console.warn('❌ 没有找到认证token，无法建立SSE连接')
    return
  }
  console.log('✅ 找到认证token，长度:', token.length)

  // 队列状态SSE连接
  if (!queueStatusEventSource.value) {
    // 创建带认证的SSE连接 - 直接连接后端服务器
    const url = `http://localhost:5000/api/v1/scheduled-publish/events/queue-status?token=${encodeURIComponent(token)}`
    console.log('🔗 正在建立队列状态SSE连接:', url)
    queueStatusEventSource.value = new EventSource(url)

    queueStatusEventSource.value.onopen = () => {
      console.log('✅ 队列状态SSE连接已建立')
    }

    // 添加连接状态监听
    setTimeout(() => {
      if (queueStatusEventSource.value?.readyState === EventSource.CONNECTING) {
        console.warn('⚠️ 队列状态SSE连接超时，仍在CONNECTING状态')
      }
    }, 5000)

    queueStatusEventSource.value.onmessage = (event) => {
      try {
        console.log('🔄 收到队列状态SSE原始数据:', event.data)
        const data = JSON.parse(event.data)
        console.log('🔄 解析后的SSE数据:', data)

        if (data.type === 'queue_status') {
          // 更新队列状态
          console.log('📊 更新前的队列状态:', JSON.stringify(queueStatus.value))
          queueStatus.value = data.data
          console.log('📊 更新后的队列状态:', JSON.stringify(queueStatus.value))
          console.log('✅ 收到队列状态更新:', data.data)
        } else if (data.type === 'heartbeat') {
          console.log('💓 收到SSE心跳包')
        } else {
          console.log('❓ 收到未知类型的SSE事件:', data.type, data)
        }
      } catch (error) {
        console.error('❌ 解析SSE队列状态数据失败:', error)
        console.error('❌ 原始数据:', event.data)
      }
    }

    queueStatusEventSource.value.onerror = (error) => {
      console.error('❌ 队列状态SSE连接错误:', error)
      console.error('❌ SSE连接状态:', queueStatusEventSource.value?.readyState)
      console.error('❌ SSE连接URL:', queueStatusEventSource.value?.url)

      // 检查是否是认证错误
      if (queueStatusEventSource.value?.readyState === EventSource.CLOSED) {
        console.error('❌ SSE连接已关闭，可能是认证失败或token过期')
        ElMessage.error('SSE连接失败，可能需要重新登录')
      }

      // 重连机制
      setTimeout(() => {
        if (queueStatusEventSource.value) {
          console.log('🔄 队列状态SSE重连中...')
          queueStatusEventSource.value.close()
          queueStatusEventSource.value = null
          initSSEConnections()
        }
      }, 5000)
    }
  }

  // 计划列表SSE连接
  if (!planListEventSource.value) {
    // 创建带认证的SSE连接
    const url = `/api/v1/scheduled-publish/events/plan-list?token=${encodeURIComponent(token)}`
    planListEventSource.value = new EventSource(url)

    planListEventSource.value.onopen = () => {
      console.log('计划列表SSE连接已建立')
    }

    planListEventSource.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'plan_list') {
          // 刷新计划列表
          loadPlans()
          console.log('收到计划列表更新事件')
        } else if (data.type === 'heartbeat') {
          console.log('收到SSE心跳包')
        }
      } catch (error) {
        console.error('解析SSE计划列表数据失败:', error)
      }
    }

    planListEventSource.value.onerror = (error) => {
      console.error('计划列表SSE连接错误:', error)
      // 重连机制
      setTimeout(() => {
        if (planListEventSource.value) {
          planListEventSource.value.close()
          planListEventSource.value = null
          initSSEConnections()
        }
      }, 5000)
    }
  }

  // 任务状态SSE连接
  if (!taskStatusEventSource.value) {
    // 创建带认证的SSE连接
    const url = `/api/v1/scheduled-publish/events/task-status?token=${encodeURIComponent(token)}`
    taskStatusEventSource.value = new EventSource(url)

    taskStatusEventSource.value.onopen = () => {
      console.log('任务状态SSE连接已建立')
    }

    taskStatusEventSource.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'task_status') {
          // 处理任务状态更新
          handleTaskStatusUpdate(data.data)
          console.log('收到任务状态更新:', data.data)
        } else if (data.type === 'heartbeat') {
          console.log('收到SSE心跳包')
        }
      } catch (error) {
        console.error('解析SSE任务状态数据失败:', error)
      }
    }

    taskStatusEventSource.value.onerror = (error) => {
      console.error('任务状态SSE连接错误:', error)
      // 重连机制
      setTimeout(() => {
        if (taskStatusEventSource.value) {
          taskStatusEventSource.value.close()
          taskStatusEventSource.value = null
          initSSEConnections()
        }
      }, 5000)
    }
  }
}

// 关闭SSE连接
const closeSSEConnections = () => {
  if (queueStatusEventSource.value) {
    queueStatusEventSource.value.close()
    queueStatusEventSource.value = null
  }
  if (planListEventSource.value) {
    planListEventSource.value.close()
    planListEventSource.value = null
  }
  if (taskStatusEventSource.value) {
    taskStatusEventSource.value.close()
    taskStatusEventSource.value = null
  }
}

// 处理任务状态更新
const handleTaskStatusUpdate = (taskData) => {
  const { task_id: taskId, status, plan_id: planId, completion_time: completionTime, error_message: errorMessage } = taskData

  // 如果任务详情对话框打开且是当前任务，更新任务详情
  if (taskDetailVisible.value && selectedTask.value && selectedTask.value.id === taskId) {
    selectedTask.value.status = status
    if (completionTime) {
      selectedTask.value.completion_time = completionTime
    }
    if (errorMessage) {
      selectedTask.value.error_message = errorMessage
    }
    // console.log('更新任务详情:', selectedTask.value)
  }

  // 如果任务列表对话框打开且是相关计划，刷新任务列表
  if (taskListVisible.value && selectedPlan.value && selectedPlan.value.id === planId) {
    loadTaskList()
    // console.log('刷新任务列表')
  }

  // 刷新计划列表以更新统计信息
  loadPlans()
  // console.log('刷新计划列表统计')
}

// 在setup中添加
const statusCheckInterval = ref(null)

// 添加开始检查状态的方法（作为SSE的备用机制）
const startStatusCheck = () => {
  // 如果已经有定时器在运行，先清除
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }

  // 设置新的定时器，每5分钟检查一次（作为SSE的备用机制）
  statusCheckInterval.value = setInterval(async () => {
    try {
      // 只有在任务详情对话框打开且页面活跃时才检查状态
      if (taskDetailVisible.value && !document.hidden && document.hasFocus()) {
        // 调用后端检查状态的接口
        await axios.post('/api/v1/scheduled-publish/tasks/check-status')
        // 刷新任务列表
        await loadTaskList()
      }
    } catch (error) {
      console.error('检查任务状态失败:', error)
    }
  }, 300000) // 5分钟检查一次，作为SSE的备用机制
}

// 添加停止检查的方法
const stopStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
    statusCheckInterval.value = null
  }
}

// 监听编辑表单关键配置的变化
watch(
  () => [
    editForm.keyword_source,
    editForm.custom_keywords,
    editForm.keyword_categories,
    editForm.selected_sites,
    editForm.use_keyword_strategy,
    editForm.keyword_selection_strategy
  ],
  (newValues, oldValues) => {
    // 只有在编辑对话框打开时才响应变化
    if (!editDialogVisible.value) return

    // 检查是否有实际变化
    const hasChanges = newValues.some((newVal, index) => {
      const oldVal = oldValues?.[index]
      if (Array.isArray(newVal) && Array.isArray(oldVal)) {
        return JSON.stringify(newVal) !== JSON.stringify(oldVal)
      }
      return newVal !== oldVal
    })

    if (hasChanges) {
      console.log('📝 检测到编辑表单关键配置变化:', {
        keyword_source: newValues[0],
        custom_keywords: newValues[1],
        keyword_categories: newValues[2],
        selected_sites: newValues[3],
        use_keyword_strategy: newValues[4],
        keyword_selection_strategy: newValues[5]
      })
      debouncedRefreshEditSiteConfigs()
    }
  },
  { deep: true }
)

// 初始化
onMounted(async () => {
  try {
    // 并行加载基础数据
    await Promise.all([
      loadKeywordCategories(),
      loadSiteCategories(),
      loadAllSites(),
      loadAiConfigs(),
      loadCommonTags(),
      loadSiteCategoryCounts()
    ])

    // 基础数据加载完成后，加载计划和队列状态
    await Promise.all([
      loadPlans(),
      loadQueueStatus()
    ])

    // 设置默认开始时间为当前时间
  const now = new Date()
  scheduleForm.scheduled_time = new Date(now.getTime())
  
    console.log('✅ 页面初始化完成')
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})

// 在组件卸载时清理
onUnmounted(() => {
  console.log('页面卸载，清理资源')
})



// 加载初始数据
const loadInitialData = async () => {
  try {
    const [keywordCategoriesRes, siteCategoriesRes, aiConfigsRes] = await Promise.all([
      apiClient.get('/v1/keyword-library/categories'),
      apiClient.get('/v1/wordpress-site/categories/list'),
      apiClient.get('/v1/ai-config/')
    ])
    keywordCategories.value = keywordCategoriesRes || []
    siteCategories.value = siteCategoriesRes?.categories || []
    aiConfigs.value = aiConfigsRes?.items || []
  } catch (error) {
    console.error('加载初始数据失败:', error)
    ElMessage.error('加载初始数据失败')
    // 设置默认值
    keywordCategories.value = []
    siteCategories.value = []
    aiConfigs.value = []
  }
}

// 加载计划列表
const loadPlans = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value
    }
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    console.log('加载计划列表，参数:', params)
    const response = await apiClient.get('/v1/scheduled-publish/plans', { params })
    console.log('计划列表响应:', response)

    planList.value = response?.items || []
    total.value = response?.total || 0

    // 调试时间字段
    if (planList.value.length > 0) {
      console.log('第一个计划的时间字段:', {
        created_at: planList.value[0].created_at,
        last_execution_time: planList.value[0].last_execution_time,
        scheduled_time: planList.value[0].scheduled_time,
        end_time: planList.value[0].end_time
      })
    }

    console.log(`成功加载 ${planList.value.length} 个计划，总数: ${total.value}`)
  } catch (error) {
    console.error('加载计划列表失败:', error)
    ElMessage.error('加载计划列表失败')
    // 设置默认值
    planList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 加载队列状态
const loadQueueStatus = async () => {
  try {
    const response = await apiClient.get('/v1/scheduled-publish/queue-status')
    queueStatus.value = {
      total_queued: response?.total_queued || 0,
      running_tasks: response?.running_tasks || 0,
      available_workers: response?.available_workers || 5,
      estimated_wait_time: response?.estimated_wait_time || null
    }
  } catch (error) {
    console.error('加载队列状态失败:', error)
    // 设置默认值，不显示错误信息，因为这个功能是新增的
    queueStatus.value = {
      total_queued: 0,
      running_tasks: 0,
      available_workers: 5,
      estimated_wait_time: null
    }
  }
}

// 事件处理
const onKeywordSourceChange = () => {
  if (scheduleForm.keyword_source === 'custom') {
    scheduleForm.keyword_categories = []
    // 清空策略配置
    clearStrategyConfig()
  } else if (scheduleForm.keyword_source === 'library') {
    scheduleForm.custom_keywords = ''
  }
  siteConfigs.value = []
}

const onSiteSourceChange = async () => {
  if (scheduleForm.site_source === 'category') {
    scheduleForm.selected_sites = []
  } else {
    // 切换到自定义选择时，清空分类选择并重新加载所有站点
    scheduleForm.site_categories = []
    
    // 重新加载所有活跃站点供自定义选择
    try {
      console.log('切换到自定义选择，重新加载所有站点...')
      const response = await apiClient.get('/v1/wordpress-site/', {
        params: { size: 100, is_active: true }
      })
      allSites.value = response?.items || []
      console.log(`重新加载了 ${allSites.value.length} 个活跃站点`)
    } catch (error) {
      console.error('加载所有站点失败:', error)
      allSites.value = []
    }
  }
  siteConfigs.value = []
}

const onKeywordCategoriesChange = () => {
  siteConfigs.value = []
}

const onSiteCategoriesChange = async () => {
  siteConfigs.value = []
  if (scheduleForm.site_categories.length > 0) {
    try {
      // 为每个分类单独获取站点，因为后端API只支持单个分类筛选
      const siteRequests = scheduleForm.site_categories.map(category =>
        apiClient.get('/v1/wordpress-site/', {
          params: { 
            size: 100, 
            is_active: true,
            category: category // 单个分类
          }
        })
      )
      
      console.log(`发起 ${siteRequests.length} 个并发请求...`)
      const responses = await Promise.all(siteRequests)
      console.log('所有请求完成，处理响应...')
      console.log('响应数组长度:', responses.length)
      
      const allSitesFromCategories = []
      responses.forEach((response, index) => {
        const category = scheduleForm.site_categories[index]
        console.log(`  处理分类 "${category}" 的响应:`)
        console.log(`    完整响应对象:`, response)
        console.log(`    响应类型:`, typeof response)
        console.log(`    响应键:`, Object.keys(response))
        console.log(`    状态码: ${response.status}`)
        console.log(`    响应数据:`, response.data)
        
        if (response?.items) {
          console.log(`    找到站点: ${response.items.length} 个`)
          response.items.forEach(site => {
            console.log(`      - ID: ${site.id}, 名称: ${site.name}, URL: ${site.url}`)
          })
          allSitesFromCategories.push(...response.items)
        } else {
          console.log(`    ❌ 响应数据格式异常:`, response)
        }
      })
      
      console.log(`所有分类获取结果: 总共 ${allSitesFromCategories.length} 个站点`)
      
      // 去重（防止站点属于多个分类时重复）
      const uniqueSites = []
      const seenIds = new Set()
      allSitesFromCategories.forEach(site => {
        if (!seenIds.has(site.id)) {
          seenIds.add(site.id)
          uniqueSites.push(site)
        }
      })
      
      allSites.value = uniqueSites
      console.log(`按分类获取到 ${uniqueSites.length} 个站点:`, uniqueSites.map(s => s.name))
    } catch (error) {
      console.error('加载站点失败:', error)
      allSites.value = []
    }
  }
}

const onSelectedSitesChange = () => {
  siteConfigs.value = []
}



// 生成站点配置
const generateSiteConfigs = async () => {
  // 表单验证
  try {
    await scheduleFormRef.value.validate()
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }
  
  generating.value = true
  try {
    let sites = []
    let keywords = []
    
    console.log('=== 开始生成站点配置 ===')
    console.log('站点来源:', scheduleForm.site_source)
    console.log('关键词来源:', scheduleForm.keyword_source)
    console.log('表单数据:', JSON.stringify(scheduleForm, null, 2))
    
    // 获取站点列表
    if (scheduleForm.site_source === 'category') {
      console.log('按分类获取站点，分类:', scheduleForm.site_categories)
      console.log('分类数组长度:', scheduleForm.site_categories.length)
      
      if (scheduleForm.site_categories.length === 0) {
        console.error('❌ 未选择站点分类')
        ElMessage.warning('请先选择站点分类')
        return
      }
      
      // 为每个分类单独获取站点
      console.log('开始为每个分类获取站点...')
      const siteRequests = scheduleForm.site_categories.map((category, index) => {
        console.log(`  准备请求分类 ${index + 1}: "${category}"`)
        return apiClient.get('/v1/wordpress-site/', {
          params: { 
            size: 100, 
            is_active: true,
            category: category // 单个分类
          }
        })
      })
      
      console.log(`发起 ${siteRequests.length} 个并发请求...`)
      const responses = await Promise.all(siteRequests)
      console.log('所有请求完成，处理响应...')
      console.log('响应数组长度:', responses.length)
      
      const allSitesFromCategories = []
      responses.forEach((response, index) => {
        const category = scheduleForm.site_categories[index]
        console.log(`  处理分类 "${category}" 的响应:`)
        console.log(`    完整响应对象:`, response)
        console.log(`    响应类型:`, typeof response)
        console.log(`    响应键:`, Object.keys(response))
        console.log(`    状态码: ${response.status}`)
        console.log(`    响应数据:`, response.data)
        
        if (response?.items) {
          console.log(`    找到站点: ${response.items.length} 个`)
          response.items.forEach(site => {
            console.log(`      - ID: ${site.id}, 名称: ${site.name}, URL: ${site.url}`)
          })
          allSitesFromCategories.push(...response.items)
        } else {
          console.log(`    ❌ 响应数据格式异常:`, response)
        }
      })
      
      console.log(`所有分类获取结果: 总共 ${allSitesFromCategories.length} 个站点`)
      
      // 去重
      const uniqueSites = []
      const seenIds = new Set()
      allSitesFromCategories.forEach(site => {
        if (!seenIds.has(site.id)) {
          seenIds.add(site.id)
          uniqueSites.push(site)
        } else {
          console.log(`    跳过重复站点: ${site.name} (ID: ${site.id})`)
        }
      })
      
      sites = uniqueSites
      console.log(`去重后站点数量: ${sites.length}`)
      sites.forEach(site => {
        console.log(`  ✓ ${site.name} (ID: ${site.id})`)
      })
    } else {
      console.log('自定义选择站点，ID:', scheduleForm.selected_sites)
      console.log('已缓存站点数量:', allSites.value.length)
      
      // 从已加载的站点中筛选
      if (allSites.value.length === 0) {
        console.log('缓存为空，加载所有活跃站点...')
        const response = await apiClient.get('/v1/wordpress-site/', {
          params: { size: 100, is_active: true }
        })
        allSites.value = response?.items || []
        console.log(`加载了 ${allSites.value.length} 个活跃站点`)
      }
      
      if (scheduleForm.selected_sites.length === 0) {
        console.error('❌ 未选择任何站点')
        ElMessage.warning('请先选择站点')
        return
      }
      
      sites = allSites.value.filter(site => scheduleForm.selected_sites.includes(site.id))
      console.log(`自定义选择了 ${sites.length} 个站点`)
      sites.forEach(site => {
        console.log(`  ✓ ${site.name} (ID: ${site.id})`)
      })
    }
    
    // 获取关键词列表
    if (scheduleForm.keyword_source === 'custom') {
      console.log('使用自定义关键词:', scheduleForm.custom_keywords)
      if (!scheduleForm.custom_keywords || scheduleForm.custom_keywords.trim() === '') {
        console.error('❌ 自定义关键词为空')
        ElMessage.warning('请输入关键词')
        return
      }
      keywords = scheduleForm.custom_keywords.split(',').map(k => k.trim()).filter(k => k)
      console.log(`解析出 ${keywords.length} 个关键词:`, keywords)
    } else if (scheduleForm.use_keyword_strategy) {
      console.log('使用智能选词策略')
      // 策略选词：生成配置时不需要预先获取关键词，每次任务执行时动态选择
      keywords = ['[智能策略选词]'] // 占位符，表示使用策略选词
      console.log('智能策略选词模式：将在任务执行时动态选择关键词')
    } else {
      console.log('从词库获取关键词，分类:', scheduleForm.keyword_categories)

      if (scheduleForm.keyword_categories.length === 0) {
        console.error('❌ 未选择关键词分类')
        ElMessage.warning('请先选择关键词分类')
        return
      }

      // 使用关键词库搜索API获取指定分类的关键词
      console.log('开始为每个关键词分类搜索关键词...')
      const searchRequests = scheduleForm.keyword_categories.map((category, index) => {
        console.log(`  准备搜索分类 ${index + 1}: "${category}"`)
        return apiClient.post('/v1/keyword-library/keywords/search', {
          page: 1,
          page_size: 100,
          category: category
        })
      })

      console.log(`发起 ${searchRequests.length} 个搜索请求...`)
      try {
        const responses = await Promise.all(searchRequests)
        console.log('所有搜索请求完成，处理响应...')
        console.log('搜索响应数组长度:', responses.length)

        keywords = []
        responses.forEach((response, index) => {
          const category = scheduleForm.keyword_categories[index]
          console.log(`  处理分类 "${category}" 的关键词:`)
          console.log(`    完整响应对象:`, response)
          console.log(`    响应类型:`, typeof response)
          console.log(`    响应键:`, Object.keys(response))
          console.log(`    状态码: ${response.status}`)
          console.log(`    响应数据:`, response.data)

          if (response?.items) {
            const categoryKeywords = response.items.map(item => item.keyword_name)
            console.log(`    找到关键词: ${categoryKeywords.length} 个`)
            keywords.push(...categoryKeywords)
          } else {
            console.log(`    ❌ 关键词响应数据格式异常:`, response)
          }
        })
        console.log(`总共找到 ${keywords.length} 个关键词`)
      } catch (error) {
        console.error('❌ 关键词API请求失败:', error)
        console.error('错误详情:', error.message)
        console.error('错误响应:', error.response)
        ElMessage.error('关键词数据加载失败: ' + (error.message || '未知错误'))
        return
      }
    }
    
    // 验证数据充足性
    console.log('\n=== 数据验证 ===')
    console.log(`可用站点数量: ${sites.length}`)
    console.log(`可用关键词数量: ${keywords.length}`)
    
    if (sites.length === 0) {
      console.error('❌ 没有找到可用的站点')
      console.log('可能的原因:')
      console.log('1. 选择的站点分类下没有活跃站点')
      console.log('2. 站点数据加载失败')
      console.log('3. 网络请求异常')
      ElMessage.warning('没有可用的站点，请检查站点分类或选择')
      return
    }
    
    if (keywords.length === 0) {
      console.error('❌ 没有找到可用的关键词')
      console.log('可能的原因:')
      console.log('1. 选择的关键词分类下没有关键词')
      console.log('2. 关键词数据加载失败')
      console.log('3. 网络请求异常')
      ElMessage.warning('没有可用的关键词，请检查关键词设置')
      return
    }
    
    console.log('✅ 数据验证通过，开始分配关键词')
    console.log(`开始为 ${sites.length} 个站点分配 ${keywords.length} 个关键词`)
    
    // 随机分配关键词给站点
    const configs = []
    for (const site of sites) {
      console.log(`\n处理站点: ${site.name} (ID: ${site.id})`)
      
      const siteKeywords = []

      // 判断是否使用动态选词
      const useSmartStrategy = scheduleForm.use_keyword_strategy // 智能筛选策略
      const useSmartRecommendation = scheduleForm.keyword_source === 'library' &&
                                     scheduleForm.keyword_selection_strategy === 'random_one' // 智能推荐

      if (useSmartStrategy || useSmartRecommendation) {
        // 策略选词或智能推荐：使用占位符，实际关键词在任务执行时动态选择
        let strategyDesc = ''
        if (useSmartStrategy && useSmartRecommendation) {
          strategyDesc = '智能策略+推荐选词'
        } else if (useSmartStrategy) {
          strategyDesc = '智能策略选词'
        } else {
          strategyDesc = '智能推荐选词'
        }
        siteKeywords.push(`[${strategyDesc}]`)
        console.log(`  + ${strategyDesc}模式：将在任务执行时动态选择关键词`)
      } else {
        // 根据关键词来源决定分配数量
        const keywordsPerSite = scheduleForm.keyword_source === 'library'
          ? Math.min(1, keywords.length) // 词库分类：每个站点只分配1个关键词
          : Math.min(3, keywords.length) // 自定义关键词：每个站点最多3个关键词

        console.log(`  关键词来源: ${scheduleForm.keyword_source}, 分配数量: ${keywordsPerSite}`)

        for (let i = 0; i < keywordsPerSite; i++) {
          let attempts = 0
          let randomKeyword
          do {
            randomKeyword = keywords[Math.floor(Math.random() * keywords.length)]
            attempts++
          } while (siteKeywords.includes(randomKeyword) && attempts < 20) // 避免重复，最多尝试20次

          if (!siteKeywords.includes(randomKeyword)) {
            siteKeywords.push(randomKeyword)
            console.log(`  + 分配关键词 ${i + 1}: "${randomKeyword}"`)
          } else {
            console.log(`  ! 无法为该站点分配更多唯一关键词`)
            break
          }
        }
      }
      
      // 获取默认博客分类
      let defaultCategoryId = null
      let defaultCategoryName = null
      if (site.blog_categories && site.blog_categories.length > 0) {
        defaultCategoryId = site.blog_categories[0].id
        defaultCategoryName = site.blog_categories[0].name
        console.log(`  + 默认博客分类: ${defaultCategoryName} (ID: ${defaultCategoryId})`)
      } else {
        console.log(`  ! 该站点没有博客分类`)
      }
      
      // 获取博客标签信息和默认标签设置
      let defaultBlogTags = []
      const blogTagsOptions = site.blog_tags || []
      
      // 如果设置了默认标签，应用到当前站点
      if (scheduleForm.default_blog_tags && scheduleForm.default_blog_tags.length > 0) {
        // 找到在当前站点标签列表中存在的默认标签
        const validDefaultTags = scheduleForm.default_blog_tags.filter(tagId => 
          blogTagsOptions.some(tag => tag.id === tagId)
        )
        defaultBlogTags = validDefaultTags
        console.log(`  + 应用默认标签: ${validDefaultTags.length} 个`)
      } else {
        console.log(`  ! 未设置默认标签`)
      }
      
      const config = {
        site_id: site.id,
        site_name: site.name,
        site_url: site.url,
        keywords: siteKeywords,
        blog_category_id: defaultCategoryId,
        blog_category_name: defaultCategoryName,
        blog_categories: site.blog_categories || [],
        blog_tags: defaultBlogTags, // 默认选中的标签ID数组
        blog_tags_options: blogTagsOptions, // 可选的标签列表
        filtered_blog_tags_options: blogTagsOptions, // 初始化过滤后的标签列表
        tagsLoading: false // 初始化标签加载状态
      }
      
      configs.push(config)
      console.log(`  ✓ 站点配置完成: ${siteKeywords.length} 个关键词`)
    }
    
    siteConfigs.value = configs
    console.log('\n=== 配置生成完成 ===')
    console.log(`成功生成 ${configs.length} 个站点配置`)
    configs.forEach(config => {
      console.log(`  - ${config.site_name}: ${config.keywords.join(', ')}`)
    })
    
    // 默认选择所有配置
    selectedConfigs.value = configs.map((_, index) => index)
    
    // 显示配置验证对话框
    configDialogVisible.value = true
    ElMessage.success(`成功生成 ${configs.length} 个站点配置，请验证后提交`)
  } catch (error) {
    console.error('❌ 生成站点配置失败:', error)
    console.error('错误详情:', error.message)
    console.error('错误堆栈:', error.stack)
    ElMessage.error('生成站点配置失败: ' + (error.message || '未知错误'))
  } finally {
    generating.value = false
  }
}

// 统一的站点配置创建函数
const createSiteConfig = (site, keywords, existingConfig = null, defaultTags = []) => {
  // 分配关键词
  const siteKeywords = existingConfig?.keywords || keywords.slice(0, Math.min(keywords.length, 3))

  // 获取博客分类 - 优先使用现有配置
  let blogCategoryId = existingConfig?.blog_category_id || null
  let blogCategoryName = existingConfig?.blog_category_name || '未分类'

  // 如果没有现有配置，使用默认分类
  if (!blogCategoryId && site.blog_categories && site.blog_categories.length > 0) {
    const defaultCategory = site.blog_categories.find(cat => cat.name === '未分类') || site.blog_categories[0]
    blogCategoryId = defaultCategory.id
    blogCategoryName = defaultCategory.name
  }

  // 获取博客标签 - 优先使用现有配置
  let blogTags = []
  if (existingConfig?.blog_tags) {
    // 确保标签ID是数字类型
    blogTags = Array.isArray(existingConfig.blog_tags)
      ? existingConfig.blog_tags.map(tag => typeof tag === 'number' ? tag : parseInt(tag)).filter(id => !isNaN(id))
      : []
  } else if (defaultTags && defaultTags.length > 0) {
    // 使用默认标签（如果有的话）
    const validDefaultTags = defaultTags.filter(tagId =>
      site.blog_tags?.some(tag => tag.id === tagId)
    )
    blogTags = validDefaultTags
  }

  const blogTagsOptions = site.blog_tags || []

  console.log(`  站点 ${site.name} 配置创建:`)
  console.log(`    分类: ${blogCategoryName} (ID: ${blogCategoryId})`)
  console.log(`    标签: ${blogTags.length} 个`, blogTags)

  return {
    site_id: site.id,
    site_name: site.name,
    site_url: site.url,
    keywords: siteKeywords,
    blog_category_id: blogCategoryId,
    blog_category_name: blogCategoryName,
    blog_categories: site.blog_categories || [],
    blog_tags: blogTags,
    blog_tags_options: blogTagsOptions,
    filtered_blog_tags_options: blogTagsOptions,
    tagsLoading: false
  }
}

// 站点配置标签搜索方法
const searchSiteConfigTags = (configIndex, query) => {
  const config = siteConfigs.value[configIndex]
  if (!config) return
  
  if (!query || !config.blog_tags_options) {
    config.filtered_blog_tags_options = config.blog_tags_options || []
    return
  }
  
  // 过滤现有标签
  config.filtered_blog_tags_options = config.blog_tags_options.filter(tag => 
    tag.name.toLowerCase().includes(query.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(query.toLowerCase()))
  )
}

// 处理站点配置标签选择变化
const handleSiteConfigTagChange = async (configIndex, selectedTagIds) => {
  const config = siteConfigs.value[configIndex]
  if (!config) return
  
  // 检查是否有新添加的字符串标签（待创建的新标签）
  const newStringTags = selectedTagIds.filter(id => typeof id === 'string')
  
  if (newStringTags.length > 0) {
    config.tagsLoading = true
    
    try {
      // 为每个新标签创建WordPress标签
      for (const tagName of newStringTags) {
        console.log(`为站点${config.site_name}创建新标签:`, tagName)
        
        const response = await apiClient.post(`/v1/wordpress-site/${config.site_id}/tags/create`, {
          name: tagName.trim()
        })
        
        if (response.success && response.data) {
          const newTag = response.data
          console.log('标签创建成功:', newTag)
          
          // 添加到站点配置的标签选项列表
          if (!config.blog_tags_options) {
            config.blog_tags_options = []
          }
          config.blog_tags_options.push(newTag)
          
          if (!config.filtered_blog_tags_options) {
            config.filtered_blog_tags_options = config.blog_tags_options
          } else {
            config.filtered_blog_tags_options.push(newTag)
          }
          
          // 将字符串ID替换为实际的数字ID
          const stringIndex = config.blog_tags.indexOf(tagName)
          if (stringIndex !== -1) {
            config.blog_tags[stringIndex] = newTag.id
          }
          
          ElMessage.success(`站点"${config.site_name}"标签"${tagName}"创建成功`)
        } else {
          console.error('标签创建失败:', response)
          ElMessage.warning(`站点"${config.site_name}"标签"${tagName}"创建失败: ${response.error || '未知错误'}`)
          
          // 从选择中移除失败的标签
          config.blog_tags = config.blog_tags.filter(id => id !== tagName)
        }
      }
    } catch (error) {
      console.error('创建标签异常:', error)
      ElMessage.error(`站点"${config.site_name}"创建标签时发生错误: ` + error.message)
      
      // 从选择中移除所有失败的标签
      config.blog_tags = config.blog_tags.filter(id => typeof id !== 'string')
    } finally {
      config.tagsLoading = false
    }
  }
}

// 查看计划任务详情
const viewPlanTasks = async (plan) => {
  selectedPlan.value = plan
  taskDetailVisible.value = true
  await loadPlanTasks(plan.id)
}

// 加载计划任务列表
const loadPlanTasks = async (planId) => {
  taskLoading.value = true
  try {
    const params = {
      page: taskCurrentPage.value,
      size: taskPageSize.value
    }
    const response = await apiClient.get(`/v1/scheduled-publish/plans/${planId}/tasks`, { params })
    const tasks = response?.items || []
    
    // 获取每个任务关联的AI文章状态和WordPress链接
    const tasksWithArticles = await Promise.all(tasks.map(async task => {
      if (task.ai_article_id) {
        try {
          // 调用AI发文的文章详情接口，获取完整信息包括WordPress链接
          const articleResponse = await apiClient.get(`/v1/ai-article/${task.ai_article_id}`)
          return {
            ...task,
            status: articleResponse.status, // 使用文章状态
            error_message: articleResponse.error_message,
            article_url: articleResponse.article_url, // WordPress文章链接
            title: articleResponse.title // 文章标题
          }
        } catch (error) {
          console.error(`获取文章详情失败 (ID: ${task.ai_article_id}):`, error)
          return task
        }
      }
      return task
    }))
    
    taskList.value = tasksWithArticles
    taskTotal.value = response?.total || 0
  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
    taskList.value = []
    taskTotal.value = 0
  } finally {
    taskLoading.value = false
  }
}

// 立即执行计划
const executePlanNow = async (plan) => {
  try {
    await ElMessageBox.confirm(
      `确定要立即执行计划 "${plan.plan_name}" 吗？`,
      '确认执行',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiClient.post(`/v1/scheduled-publish/execute-plan/${plan.id}`)
    ElMessage.success('计划已加入执行队列')
    await loadQueueStatus()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行计划失败:', error)
      ElMessage.error('执行计划失败，功能正在开发中')
    }
  }
}

// 删除计划
const deletePlan = async (plan) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除计划 "${plan.plan_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiClient.delete(`/v1/scheduled-publish/plans/${plan.id}`)
    ElMessage.success('计划已删除')
    await loadPlans()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除计划失败:', error)
      ElMessage.error('删除计划失败，功能正在开发中')
    }
  }
}

// 查看文章
const viewArticle = async (articleId) => {
  // 尝试从任务列表中找到对应的文章信息
  const task = taskList.value.find(t => t.ai_article_id === articleId)
  
  if (task && task.article_url) {
    // 如果任务中已有WordPress文章链接，直接打开
    window.open(task.article_url, '_blank')
  } else {
    // 如果没有，获取文章详情并打开WordPress链接
    try {
      const articleResponse = await apiClient.get(`/v1/ai-article/${articleId}`)
      
      if (articleResponse.article_url) {
        // 打开WordPress站点的文章链接
        window.open(articleResponse.article_url, '_blank')
      } else {
        // 如果还没有WordPress链接，说明文章可能还在生成中
        ElMessage.warning('文章尚未发布到WordPress站点')
      }
    } catch (error) {
      console.error('获取文章详情失败:', error)
      ElMessage.error('获取文章信息失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadPlans()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadPlans()
}

const handleTaskSizeChange = (val) => {
  taskPageSize.value = val
  taskCurrentPage.value = 1
  if (selectedPlan.value) {
    loadPlanTasks(selectedPlan.value.id)
  }
}

const handleTaskCurrentChange = (val) => {
  taskCurrentPage.value = val
  if (selectedPlan.value) {
    loadPlanTasks(selectedPlan.value.id)
  }
}

// 搜索处理
const handleSearch = () => {
  // 这里可以实现搜索逻辑
  loadPlans()
}

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 禁用结束时间（必须晚于开始时间）
const disabledEndDate = (time) => {
  if (!scheduleForm.scheduled_time) {
    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
  }
  return time.getTime() < new Date(scheduleForm.scheduled_time).getTime()
}

// 频率类型变化处理
const onFrequencyTypeChange = (value) => {
  // 重置相关字段
  scheduleForm.weekly_days = []
  scheduleForm.custom_interval_value = 1
  scheduleForm.custom_interval_unit = 'days'
  scheduleForm.daily_time = null
  scheduleForm.max_executions = null
  
  // 根据频率类型设置默认值
  if (value === 'daily') {
    // 设置默认执行时间为当前时间
    const now = new Date()
    scheduleForm.daily_time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
  } else if (value === 'weekly') {
    // 默认选择工作日
    scheduleForm.weekly_days = ['1', '2', '3', '4', '5']
  }
}

// 获取周几的文本显示
const getWeeklyDaysText = () => {
  if (!scheduleForm.weekly_days || scheduleForm.weekly_days.length === 0) {
    return '未选择'
  }
  
  const dayNames = {
    0: '周日',
    1: '周一', 
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六'
  }
  
  return scheduleForm.weekly_days.map(day => dayNames[day]).join('、')
}

// 获取间隔单位的文本显示
const getIntervalUnitText = () => {
  const unitNames = {
    hours: '小时',
    days: '天',
    weeks: '周'
  }
  return unitNames[scheduleForm.custom_interval_unit] || '天'
}

// 获取频率文本显示
const getFrequencyText = (frequencyType) => {
  const frequencyTexts = {
    once: '仅一次',
    daily: '每天',
    weekly: '每周',
    custom: '自定义'
  }
  return frequencyTexts[frequencyType] || '未知'
}

// 获取频率标签类型
const getFrequencyTagType = (frequencyType) => {
  const tagTypes = {
    once: 'info',
    daily: 'success',
    weekly: 'warning',
    custom: 'primary'
  }
  return tagTypes[frequencyType] || 'info'
}

// 格式化日期 - 使用全局时区配置
const formatDate = formatTableDateTime
const formatDateTime = (datetime) => {
  console.log('formatDateTime 调用，输入:', datetime, '类型:', typeof datetime)
  if (!datetime) {
    console.log('formatDateTime 返回 "-" (空值)')
    return '-'
  }
  const result = formatDateTimeWithTimezone(datetime)
  console.log('formatDateTime 结果:', result)
  return result
}

// 格式化等待时间
const formatWaitTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`
}

// 获取关键词策略文本
const getKeywordStrategyText = (task) => {
  // 从任务的关键词判断策略类型
  if (task.keywords) {
    if (task.keywords.includes('[智能策略+推荐选词]')) {
      return '智能策略+推荐'
    } else if (task.keywords.includes('[智能策略选词]')) {
      return '智能策略选词'
    } else if (task.keywords.includes('[智能推荐选词]')) {
      return '智能推荐选词'
    } else if (task.keywords.startsWith('[') && task.keywords.endsWith(']')) {
      return '动态选词'
    } else {
      return '固定关键词'
    }
  }
  return '未设置'
}

// 获取实际关键词
const getActualKeyword = (task) => {
  // 如果有实际选择的关键词，优先显示
  if (task.actual_keyword) {
    return task.actual_keyword
  }

  // 如果是动态选词但还没有实际关键词，显示策略描述
  if (task.keywords && (task.keywords.startsWith('[') && task.keywords.endsWith(']'))) {
    return task.keywords
  }

  // 否则显示原始关键词
  return task.keywords || '-'
}

// 获取状态类型
const getStatusType = (status) => {
  const statusTypes = {
    pending: 'info',
    queued: 'warning',
    running: 'primary',
    success: 'success',
    failed: 'danger'
  }
  return statusTypes[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusTexts = {
    pending: '待执行',
    queued: '排队中',
    running: '执行中',
    success: '成功',
    failed: '失败'
  }
  return statusTexts[status] || status
}

// 获取站点分类显示
const getSiteCategories = (categories) => {
  if (!categories) return []
  if (typeof categories === 'string') {
    try {
      return JSON.parse(categories)
    } catch {
      return categories.split(',').map(c => c.trim()).filter(c => c)
    }
  }
  return Array.isArray(categories) ? categories : []
}

// 获取站点数量
const getSiteCount = (sites) => {
  if (!sites) return 0
  if (typeof sites === 'string') {
    try {
      const parsed = JSON.parse(sites)
      return Array.isArray(parsed) ? parsed.length : 0
    } catch {
      return sites.split(',').filter(s => s.trim()).length
    }
  }
  return Array.isArray(sites) ? sites.length : 0
}

// 获取总站点数量（用于站点数量列显示）
const getTotalSiteCount = (row) => {
  // 如果是按分类选择，计算所有分类下的站点总数
  if (row.site_categories && getSiteCategories(row.site_categories).length > 0) {
    const categories = getSiteCategories(row.site_categories)
    let totalCount = 0
    categories.forEach(category => {
      totalCount += siteCategoryCounts.value[category] || 0
    })
    return totalCount
  } else if (row.selected_sites && getSiteCount(row.selected_sites) > 0) {
    // 如果是自定义选择，直接返回选择的站点数量
    return getSiteCount(row.selected_sites)
  }
  return 0
}

// 处理配置选择
const handleConfigSelect = (index, checked) => {
  if (checked) {
    if (!selectedConfigs.value.includes(index)) {
      selectedConfigs.value.push(index)
    }
  } else {
    const idx = selectedConfigs.value.indexOf(index)
    if (idx > -1) {
      selectedConfigs.value.splice(idx, 1)
    }
  }
}

// 处理配置全选
const handleSelectAll = (checked) => {
  if (configTableRef.value) {
    if (checked) {
      // 选择所有行
      siteConfigs.value.forEach(row => {
        configTableRef.value.toggleRowSelection(row, true)
      })
    } else {
      // 取消选择所有行
      configTableRef.value.clearSelection()
    }
  }
}

// 处理配置对话框关闭
const handleConfigDialogClose = (done) => {
  ElMessageBox.confirm('确定要关闭配置验证吗？未保存的更改将丢失。', '确认关闭', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    configDialogVisible.value = false
    if (done) done()
  }).catch(() => {
    // 用户取消关闭
  })
}

// 移除关键词
const removeKeyword = (configIndex, keywordIndex) => {
  if (siteConfigs.value[configIndex] && siteConfigs.value[configIndex].keywords) {
    siteConfigs.value[configIndex].keywords.splice(keywordIndex, 1)
    ElMessage.success('关键词已移除')
  }
}

// 显示添加关键词对话框
const showAddKeywordDialog = (index) => {
  currentConfigIndex.value = index
  addKeywordForm.keyword = ''
  addKeywordDialogVisible.value = true
}

// 添加关键词
const addKeyword = () => {
  if (!addKeywordForm.keyword.trim()) {
    ElMessage.warning('请输入关键词')
    return
  }
  
  const configIndex = currentConfigIndex.value
  if (configIndex >= 0 && siteConfigs.value[configIndex]) {
    const config = siteConfigs.value[configIndex]
    if (config.keywords.includes(addKeywordForm.keyword.trim())) {
      ElMessage.warning('该关键词已存在')
      return
    }
    
    config.keywords.push(addKeywordForm.keyword.trim())
    addKeywordDialogVisible.value = false
    ElMessage.success('关键词已添加')
  }
}

// 重置配置
const resetConfigs = () => {
  ElMessageBox.confirm('确定要重新生成配置吗？当前的修改将丢失。', '确认重新生成', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    configDialogVisible.value = false
    generateSiteConfigs()
  }).catch(() => {
    // 用户取消
  })
}

// 提交配置
const submitConfigs = async () => {
  if (selectedConfigs.value.length === 0) {
    ElMessage.warning('请至少选择一个站点配置')
    return
  }
  
  submitting.value = true
  try {
    const selectedSiteConfigs = selectedConfigs.value.map(index => siteConfigs.value[index])
    
    // 处理每个站点配置中的新标签创建
    const processedConfigs = []
    
    for (const config of selectedSiteConfigs) {
      let finalBlogTags = [...(config.blog_tags || [])]
      
      if (finalBlogTags.length > 0) {
        // 分离现有标签ID和新标签名称
        const existingTagIds = finalBlogTags.filter(tag => typeof tag === 'number')
        const newTagNames = finalBlogTags.filter(tag => typeof tag === 'string' && tag.trim() !== '')
        
        // 如果有新标签需要创建
        if (newTagNames.length > 0) {
          try {
            ElMessage.info(`正在为站点 "${config.site_name}" 创建 ${newTagNames.length} 个新标签...`)
            const createdTags = await createNewTagsForSchedule(newTagNames, config.site_id)
            
            // 更新站点配置的标签选项列表
            if (!config.blog_tags_options) {
              config.blog_tags_options = []
            }
            config.blog_tags_options.push(...createdTags)
            config.filtered_blog_tags_options = config.blog_tags_options
            
            // 合并现有标签ID和新创建的标签ID
            finalBlogTags = [...existingTagIds, ...createdTags.map(tag => tag.id)]
          } catch (error) {
            ElMessage.error(`为站点 "${config.site_name}" 创建新标签失败，跳过该站点配置`)
            continue // 跳过这个站点配置
          }
        } else {
          finalBlogTags = existingTagIds
        }
      }
      
      // 添加处理后的配置
      processedConfigs.push({
        site_id: config.site_id,
        site_name: config.site_name,
        site_url: config.site_url,
        keywords: config.keywords,
        blog_category_id: config.blog_category_id,
        blog_category_name: config.blog_category_name,
        blog_tags: finalBlogTags // 使用处理后的标签ID数组
      })
    }
    
    if (processedConfigs.length === 0) {
      ElMessage.warning('所有站点配置处理失败，无法提交')
      return
    }
    
    const requestData = {
      plan_name: scheduleForm.plan_name,
      keyword_source: scheduleForm.keyword_source, // 保持原始关键词来源
      custom_keywords: scheduleForm.keyword_source === 'custom' ? scheduleForm.custom_keywords : null,
      keyword_categories: scheduleForm.keyword_source === 'library' ? scheduleForm.keyword_categories : null,
      keyword_selection_strategy: scheduleForm.keyword_source === 'library' ? scheduleForm.keyword_selection_strategy : null,

      // 选词策略配置 - 无论keyword_source是什么，都保存策略配置
      use_keyword_strategy: scheduleForm.use_keyword_strategy || false,
      strategy_intent: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_intent : null,
      strategy_volume_min: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_volume_min : null,
      strategy_volume_max: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_volume_max : null,
      strategy_difficulty_min: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_difficulty_min : null,
      strategy_difficulty_max: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_difficulty_max : null,
      strategy_cpc_min: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_cpc_min : null,
      strategy_cpc_max: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_cpc_max : null,
      strategy_competitive_density_min: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_competitive_density_min : null,
      strategy_competitive_density_max: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_competitive_density_max : null,
      strategy_countries: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_countries : null,
      strategy_categories: scheduleForm.use_keyword_strategy ? scheduleForm.strategy_categories : null,

      // 站点选择（现在直接使用站点ID数组）
      site_source: 'custom', // 统一使用custom模式
      site_categories: null,
      selected_sites: scheduleForm.selected_sites,
      site_configs: processedConfigs, // 发送对象数组，与后端schema一致
      default_blog_tags: scheduleForm.default_blog_tags || [],
      ai_model: scheduleForm.ai_model,
      ai_config_id: scheduleForm.ai_config_id,
      scheduled_time: toUTCString(scheduleForm.scheduled_time),
      end_time: scheduleForm.end_time ? toUTCString(scheduleForm.end_time) : null,
      frequency_type: scheduleForm.frequency_type,
      weekly_days: scheduleForm.weekly_days,
      custom_interval_value: scheduleForm.custom_interval_value,
      custom_interval_unit: scheduleForm.custom_interval_unit,
      daily_time: scheduleForm.daily_time,
      max_executions: scheduleForm.max_executions
    }
    
    console.log('=== 提交配置数据 ===')
    console.log('智能策略配置状态:', {
      use_keyword_strategy: scheduleForm.use_keyword_strategy,
      strategy_intent: scheduleForm.strategy_intent,
      strategy_volume_min: scheduleForm.strategy_volume_min,
      strategy_volume_max: scheduleForm.strategy_volume_max,
      strategy_difficulty_min: scheduleForm.strategy_difficulty_min,
      strategy_difficulty_max: scheduleForm.strategy_difficulty_max,
      strategy_countries: scheduleForm.strategy_countries,
      strategy_categories: scheduleForm.strategy_categories
    })
    console.log('请求数据:', JSON.stringify(requestData, null, 2))
    console.log('选择的配置数量:', selectedConfigs.value.length)
    console.log('实际处理的配置数量:', processedConfigs.length)
    console.log('API端点: /v1/scheduled-publish/generate-batch-tasks')
    
    const response = await apiClient.post('/v1/scheduled-publish/generate-batch-tasks', requestData)
    console.log('提交成功，响应:', response)
    
    ElMessage.success(`成功提交 ${processedConfigs.length} 个站点配置`)
    
    // 关闭对话框并重置表单
    configDialogVisible.value = false
    resetForm()
    await loadPlans()
    await loadQueueStatus()
  } catch (error) {
    console.error('❌ 提交配置失败:', error)
    console.error('错误详情:', error.message)
    console.error('错误响应:', error.response)
    console.error('错误状态:', error.response?.status)
    console.error('错误数据:', error.response?.data)
    
    if (error.response?.status === 404) {
      ElMessage.error('API端点不存在，功能正在开发中')
    } else if (error.response?.status === 422) {
      ElMessage.error('提交数据格式错误: ' + (error.response?.data?.detail || '未知错误'))
    } else {
      ElMessage.error('提交配置失败: ' + (error.message || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  scheduleFormRef.value?.resetFields()
  siteConfigs.value = []
  selectedConfigs.value = []
  
  // 手动重置表单数据
  Object.assign(scheduleForm, {
    plan_name: '',
    keyword_source: 'library', // 默认选择从词库选择
    custom_keywords: '',
    keyword_categories: [],
    keyword_selection_strategy: 'random_one', // 重置智能推荐策略

    // 重置选词策略配置
    use_keyword_strategy: false, // 重置智能策略开关
    strategy_intent: '',
    strategy_volume_min: null,
    strategy_volume_max: null,
    strategy_difficulty_min: null,
    strategy_difficulty_max: null,
    strategy_cpc_min: null,
    strategy_cpc_max: null,
    strategy_competitive_density_min: null,
    strategy_competitive_density_max: null,
    strategy_countries: [],
    strategy_categories: [],

    selected_sites: [],
    ai_model: '',
    ai_config_id: null,
    default_blog_tags: [], // 重置默认标签
    scheduled_time: null,
    end_time: null,
    frequency_type: 'once',
    weekly_days: [],
    custom_interval_value: 1,
    custom_interval_unit: 'days',
    daily_time: null,
    max_executions: null
  })
  
  // 重新设置默认时间为当前时间
  const now = new Date()
  scheduleForm.scheduled_time = new Date(now.getTime())
}

// 修改查看任务详情的方法
const viewTasks = async (plan) => {
  selectedPlan.value = plan
  taskDetailVisible.value = true
  await loadTaskList()
  // 开始状态检查
  startStatusCheck()
}

// 修改关闭任务详情对话框的处理
const handleTaskDialogClose = () => {
  taskDetailVisible.value = false
  selectedPlan.value = null
  // 停止状态检查
  stopStatusCheck()
}

// 编辑计划
const editPlan = async (plan) => {
  // 解析站点配置数据
  const siteCategories = plan.site_categories ? (typeof plan.site_categories === 'string' ? JSON.parse(plan.site_categories) : plan.site_categories) : []
  const selectedSites = plan.selected_sites ? (typeof plan.selected_sites === 'string' ? JSON.parse(plan.selected_sites) : plan.selected_sites) : []

  // 合并分类和单独站点的选择
  const allSelectedSiteIds = new Set()

  // 添加分类下的所有站点
  if (siteCategories.length > 0) {
    siteCategories.forEach(category => {
      const categorySites = allSites.value.filter(site => site.category === category)
      categorySites.forEach(site => allSelectedSiteIds.add(site.id))
    })
  }

  // 添加单独选择的站点
  if (selectedSites.length > 0) {
    selectedSites.forEach(siteId => allSelectedSiteIds.add(siteId))
  }

  const finalSelectedSites = Array.from(allSelectedSiteIds)

  // 判断关键词来源
  let keywordSource = 'custom'
  if (plan.keyword_categories && (Array.isArray(plan.keyword_categories) ? plan.keyword_categories.length > 0 : plan.keyword_categories)) {
    keywordSource = 'library'
  }

  // 填充编辑表单
  Object.assign(editForm, {
    id: plan.id,
    plan_name: plan.plan_name,
    keyword_source: keywordSource,
    custom_keywords: plan.keywords || '',
    keyword_categories: plan.keyword_categories ? (typeof plan.keyword_categories === 'string' ? JSON.parse(plan.keyword_categories) : plan.keyword_categories) : [],

    selected_sites: finalSelectedSites, // 使用合并后的站点ID数组
    frequency_type: plan.frequency_type || 'once',
    weekly_days: plan.weekly_days ? (typeof plan.weekly_days === 'string' ? JSON.parse(plan.weekly_days) : plan.weekly_days) : [],
    custom_interval_value: plan.custom_interval_value || 1,
    custom_interval_unit: plan.custom_interval_unit || 'days',
    daily_time: plan.daily_time || null,
    scheduled_time: plan.scheduled_time ? fromUTCString(plan.scheduled_time) : null,
    end_time: plan.end_time ? fromUTCString(plan.end_time) : null,
    max_executions: plan.max_executions || null,
    is_active: plan.is_active !== false
  })

  // 加载智能选词策略配置
  editForm.keyword_selection_strategy = plan.keyword_selection_strategy || 'random_one'
  editForm.use_keyword_strategy = plan.use_keyword_strategy || false
  editForm.strategy_intent = plan.strategy_intent || ''
  editForm.strategy_volume_min = plan.strategy_volume_min
  editForm.strategy_volume_max = plan.strategy_volume_max
  editForm.strategy_difficulty_min = plan.strategy_difficulty_min
  editForm.strategy_difficulty_max = plan.strategy_difficulty_max
  editForm.strategy_cpc_min = plan.strategy_cpc_min
  editForm.strategy_cpc_max = plan.strategy_cpc_max
  editForm.strategy_competitive_density_min = plan.strategy_competitive_density_min
  editForm.strategy_competitive_density_max = plan.strategy_competitive_density_max
  editForm.strategy_countries = plan.strategy_countries ? (typeof plan.strategy_countries === 'string' ? JSON.parse(plan.strategy_countries) : plan.strategy_countries) : []
  editForm.strategy_categories = plan.strategy_categories ? (typeof plan.strategy_categories === 'string' ? JSON.parse(plan.strategy_categories) : plan.strategy_categories) : []

  // 调试信息：检查计划数据中的策略配置
  console.log('编辑计划 - 原始计划数据:', {
    id: plan.id,
    name: plan.plan_name,
    use_keyword_strategy: plan.use_keyword_strategy,
    strategy_intent: plan.strategy_intent,
    strategy_volume_min: plan.strategy_volume_min,
    strategy_volume_max: plan.strategy_volume_max,
    strategy_difficulty_min: plan.strategy_difficulty_min,
    strategy_difficulty_max: plan.strategy_difficulty_max,
    strategy_cpc_min: plan.strategy_cpc_min,
    strategy_cpc_max: plan.strategy_cpc_max,
    strategy_competitive_density_min: plan.strategy_competitive_density_min,
    strategy_competitive_density_max: plan.strategy_competitive_density_max,
    strategy_countries: plan.strategy_countries,
    strategy_categories: plan.strategy_categories
  })

  console.log('编辑计划 - 加载到editForm的数据:', {
    use_keyword_strategy: editForm.use_keyword_strategy,
    strategy_intent: editForm.strategy_intent,
    strategy_volume_min: editForm.strategy_volume_min,
    strategy_volume_max: editForm.strategy_volume_max,
    strategy_difficulty_min: editForm.strategy_difficulty_min,
    strategy_difficulty_max: editForm.strategy_difficulty_max,
    strategy_cpc_min: editForm.strategy_cpc_min,
    strategy_cpc_max: editForm.strategy_cpc_max,
    strategy_competitive_density_min: editForm.strategy_competitive_density_min,
    strategy_competitive_density_max: editForm.strategy_competitive_density_max,
    strategy_countries: editForm.strategy_countries,
    strategy_categories: editForm.strategy_categories
  })

  // 先打开对话框
  editDialogVisible.value = true

  // 然后加载编辑站点配置
  console.log('准备加载编辑站点配置，计划数据:', plan)
  console.log('最终选中的站点:', finalSelectedSites)
  await loadEditSiteConfigs(plan, finalSelectedSites)
  console.log('加载完成后的 siteConfigs:', siteConfigs.value)

  // 延迟设置树形选择状态，确保对话框已打开
  setTimeout(() => {
    setEditSiteTreeChecked(finalSelectedSites)
  }, 100)
}

// 编辑表单关键词来源变化
const onEditKeywordSourceChange = () => {
  // 只清空当前不使用的字段，保留用户之前的选择
  if (editForm.keyword_source === 'custom') {
    // 切换到自定义关键词时，不清空词库分类选择，保留用户之前的选择
    // editForm.keyword_categories = [] // 注释掉这行，保留词库分类选择
    // 但是要确保关闭智能策略，因为自定义关键词不使用策略
    editForm.use_keyword_strategy = false
    console.log('切换到自定义关键词，关闭智能策略')
  } else if (editForm.keyword_source === 'library') {
    // 切换到词库选择时，清空自定义关键词
    editForm.custom_keywords = ''
  }
}



// 树形选择处理
const onSiteTreeCheck = (data, checked) => {
  console.log('站点树选择变化:', data, checked)

  // 获取所有选中的节点
  const checkedNodes = siteTreeRef.value.getCheckedNodes()
  const selectedSites = []
  const selectedSiteNames = []

  checkedNodes.forEach(node => {
    if (node.type === 'site') {
      selectedSites.push(node.siteId)
      selectedSiteNames.push(node.label)
    }
  })

  // 更新表单数据
  scheduleForm.selected_sites = selectedSites
  siteConfigs.value = []

  console.log('更新选中的站点ID:', selectedSites)
  console.log('更新选中的站点名称:', selectedSiteNames)
}

// 编辑表单树形选择处理
const onEditSiteTreeCheck = (data, checked) => {
  console.log('编辑表单站点树选择变化:', data, checked)

  // 获取所有选中的节点
  const checkedNodes = editSiteTreeRef.value.getCheckedNodes()
  const selectedSites = []
  const selectedSiteNames = []

  checkedNodes.forEach(node => {
    if (node.type === 'site') {
      selectedSites.push(node.siteId)
      selectedSiteNames.push(node.label)
    }
  })

  // 更新编辑表单数据
  editForm.selected_sites = selectedSites

  console.log('更新编辑表单选中的站点ID:', selectedSites)
  console.log('更新编辑表单选中的站点名称:', selectedSiteNames)

  // 触发右侧配置自动刷新
  console.log('🔄 站点选择变化，触发配置自动刷新')
  debouncedRefreshEditSiteConfigs()
}

// 获取选中站点数量
const getSelectedSiteCount = () => {
  return scheduleForm.selected_sites.length
}

// 获取编辑表单选中站点数量
const getEditSelectedSiteCount = () => {
  return editForm.selected_sites.length
}

// 设置树形选择状态（用于编辑时回显）
const setSiteTreeChecked = (selectedSiteIds) => {
  if (!siteTreeRef.value || !selectedSiteIds) return

  // 清除所有选择
  siteTreeRef.value.setCheckedKeys([])

  // 设置选中的站点
  const checkedKeys = selectedSiteIds.map(id => `site-${id}`)
  siteTreeRef.value.setCheckedKeys(checkedKeys)
}

// 设置编辑表单树形选择状态
const setEditSiteTreeChecked = (selectedSiteIds) => {
  if (!editSiteTreeRef.value || !selectedSiteIds) return

  // 清除所有选择
  editSiteTreeRef.value.setCheckedKeys([])

  // 设置选中的站点
  const checkedKeys = selectedSiteIds.map(id => `site-${id}`)
  editSiteTreeRef.value.setCheckedKeys(checkedKeys)
}

// 下拉框显示/隐藏处理
const onSiteSelectVisibleChange = (visible) => {
  if (visible && siteTreeRef.value) {
    // 下拉框打开时，同步树形选择状态
    setTimeout(() => {
      setSiteTreeChecked(scheduleForm.selected_sites)
    }, 100)
  }
}

const onEditSiteSelectVisibleChange = (visible) => {
  if (visible && editSiteTreeRef.value) {
    // 下拉框打开时，同步树形选择状态
    setTimeout(() => {
      setEditSiteTreeChecked(editForm.selected_sites)
    }, 100)
  }
}

// 清空选择
const onSiteSelectClear = () => {
  scheduleForm.selected_sites = []
  siteConfigs.value = []
  if (siteTreeRef.value) {
    siteTreeRef.value.setCheckedKeys([])
  }
}

const onEditSiteSelectClear = () => {
  editForm.selected_sites = []
  siteConfigs.value = [] // 清空右侧配置
  if (editSiteTreeRef.value) {
    editSiteTreeRef.value.setCheckedKeys([])
  }
}

// 树形节点过滤方法
const filterSiteNode = (value, data) => {
  if (!value) return true
  const searchValue = value.toLowerCase()

  // 搜索分类名称或站点名称、URL
  if (data.type === 'category') {
    return data.label.toLowerCase().includes(searchValue)
  } else {
    return data.label.toLowerCase().includes(searchValue) ||
           (data.url && data.url.toLowerCase().includes(searchValue))
  }
}

// 编辑表单频率变化
const onEditFrequencyChange = () => {
  if (editForm.frequency_type === 'once') {
    editForm.weekly_days = []
    editForm.custom_interval_value = 1
    editForm.custom_interval_unit = 'days'
    editForm.daily_time = null
    editForm.max_executions = null
  }
}

// 关闭编辑对话框
const handleEditDialogClose = () => {
  editDialogVisible.value = false
  editFormRef.value?.resetFields()

  // 清理自动刷新定时器
  if (editAutoRefreshTimer.value) {
    clearTimeout(editAutoRefreshTimer.value)
    editAutoRefreshTimer.value = null
  }

  // 重置站点配置（复用 siteConfigs）
  siteConfigs.value = []
  editGenerating.value = false

  // 重置策略配置状态
  scheduleForm.use_keyword_strategy = false
  clearStrategyConfig()
}

// 提交编辑表单
const submitEditForm = async () => {
  try {
    await editFormRef.value.validate()
    editSubmitting.value = true

    // 调试：打印当前站点配置
    console.log('=== 提交编辑表单 - 站点配置调试 ===')
    console.log('siteConfigs.value:', siteConfigs.value)
    siteConfigs.value.forEach((config, index) => {
      console.log(`站点 ${index + 1}: ${config.site_name}`)
      console.log(`  分类: ${config.blog_category_name} (ID: ${config.blog_category_id})`)
      console.log(`  标签: ${config.blog_tags?.length || 0} 个`, config.blog_tags)
      console.log(`  关键词: ${config.keywords?.join(', ') || '无'}`)
    })

    console.log('即将保存的站点配置对象数组:', JSON.stringify(siteConfigs.value, null, 2))

    // 准备提交数据
    const updateData = {
      plan_name: editForm.plan_name,
      keyword_source: editForm.use_keyword_strategy ? 'strategy' : editForm.keyword_source,
      keywords: editForm.keyword_source === 'custom' ? editForm.custom_keywords : null,
      keyword_categories: editForm.keyword_source === 'library' ? editForm.keyword_categories : null,
      keyword_selection_strategy: editForm.keyword_source === 'library' ? editForm.keyword_selection_strategy : null,

      // 选词策略配置（从编辑表单获取）
      use_keyword_strategy: editForm.use_keyword_strategy || false,
      strategy_intent: editForm.use_keyword_strategy ? editForm.strategy_intent : null,
      strategy_volume_min: editForm.use_keyword_strategy ? editForm.strategy_volume_min : null,
      strategy_volume_max: editForm.use_keyword_strategy ? editForm.strategy_volume_max : null,
      strategy_difficulty_min: editForm.use_keyword_strategy ? editForm.strategy_difficulty_min : null,
      strategy_difficulty_max: editForm.use_keyword_strategy ? editForm.strategy_difficulty_max : null,
      strategy_cpc_min: editForm.use_keyword_strategy ? editForm.strategy_cpc_min : null,
      strategy_cpc_max: editForm.use_keyword_strategy ? editForm.strategy_cpc_max : null,
      strategy_competitive_density_min: editForm.use_keyword_strategy ? editForm.strategy_competitive_density_min : null,
      strategy_competitive_density_max: editForm.use_keyword_strategy ? editForm.strategy_competitive_density_max : null,
      strategy_countries: editForm.use_keyword_strategy ? editForm.strategy_countries : null,
      strategy_categories: editForm.use_keyword_strategy ? editForm.strategy_categories : null,

      // 站点选择（现在直接使用站点ID数组）
      site_categories: null,
      selected_sites: editForm.selected_sites,
      site_configs: siteConfigs.value, // 发送对象数组，与后端schema一致
      frequency_type: editForm.frequency_type,
      weekly_days: editForm.frequency_type === 'weekly' ? editForm.weekly_days : null,
      custom_interval_value: editForm.frequency_type === 'custom' ? editForm.custom_interval_value : null,
      custom_interval_unit: editForm.frequency_type === 'custom' ? editForm.custom_interval_unit : null,
      daily_time: editForm.frequency_type === 'daily' ? editForm.daily_time : null,
      scheduled_time: editForm.scheduled_time ? toUTCString(editForm.scheduled_time) : null,
      end_time: editForm.end_time ? toUTCString(editForm.end_time) : null,
      max_executions: editForm.frequency_type !== 'once' ? editForm.max_executions : null,
      is_active: editForm.is_active
    }

    await apiClient.put(`/v1/scheduled-publish/plans/${editForm.id}`, updateData)
    ElMessage.success('计划更新成功')
    editDialogVisible.value = false
    await loadPlans() // 重新加载计划列表
  } catch (error) {
    console.error('更新计划失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('更新计划失败')
    }
  } finally {
    editSubmitting.value = false
  }
}

// 刷新所有数据（合并了统计刷新和数据刷新）
const refreshAllData = async () => {
  if (refreshing.value) return

  refreshing.value = true
  try {
    console.log('🔄 开始刷新所有数据...')

    // 并行执行：刷新统计信息 + 重新加载数据
    await Promise.all([
      apiClient.post('/v1/scheduled-publish/refresh-all-statistics'),
      loadPlans(),
      loadQueueStatus()
    ])

    console.log('✅ 所有数据刷新完成')
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('❌ 刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    refreshing.value = false
  }
}

// 清理队列
const cleanupQueue = async () => {
  cleaning.value = true
  try {
    // 先更新队列状态
    await apiClient.post('/v1/scheduled-publish/tasks/update-queue-status')
    // 再清理已完成的队列项
    await apiClient.post('/v1/scheduled-publish/cleanup-queue')
    ElMessage.success('队列清理完成')
    await loadQueueStatus() // 重新加载队列状态
  } catch (error) {
    console.error('清理队列失败:', error)
    ElMessage.error('清理队列失败')
  } finally {
    cleaning.value = false
  }
}



// 加载AI配置
const loadAiConfigs = async () => {
  try {
    const response = await apiClient.get('/v1/ai-config/', {
      params: { size: 100, is_active: true }
    })
    aiConfigs.value = response?.items || []
  } catch (error) {
    console.error('加载AI配置失败:', error)
    ElMessage.error('加载AI配置失败')
  }
}

// 加载通用标签
const loadCommonTags = async () => {
  try {
    // 获取所有站点的标签，构建通用标签列表
    const response = await apiClient.get('/v1/wordpress-site/', {
      params: { size: 100, is_active: true }
    })
    const sites = response?.items || []
    
    // 收集所有标签并去重
    const allTags = []
    const tagMap = new Map()
    
    sites.forEach(site => {
      if (site.blog_tags && Array.isArray(site.blog_tags)) {
        site.blog_tags.forEach(tag => {
          if (!tagMap.has(tag.id)) {
            tagMap.set(tag.id, {
              id: tag.id,
              name: tag.name,
              count: tag.count || 0,
              description: tag.description || ''
            })
          }
        })
      }
    })
    
    commonTags.value = Array.from(tagMap.values())
    console.log(`加载了 ${commonTags.value.length} 个通用标签`)
  } catch (error) {
    console.error('加载通用标签失败:', error)
    commonTags.value = []
  }
}

// 调试站点配置
const debugSiteConfigs = () => {
  console.log('=== 调试站点配置 ===')
  console.log('当前 siteConfigs.value:', siteConfigs.value)

  if (siteConfigs.value.length === 0) {
    ElMessage.info('当前没有站点配置')
    return
  }

  let debugInfo = '当前站点配置:\n\n'
  siteConfigs.value.forEach((config, index) => {
    debugInfo += `站点 ${index + 1}: ${config.site_name}\n`
    debugInfo += `  分类: ${config.blog_category_name} (ID: ${config.blog_category_id})\n`
    debugInfo += `  标签: ${config.blog_tags?.length || 0} 个 ${(config.blog_tags && config.blog_tags.map(tag => `[${tag}]`).join(' ')) || ''}\n`
    debugInfo += `  关键词: ${config.keywords?.join(', ') || '无'}\n\n`
  })

  // 显示调试信息
  ElMessageBox.alert(debugInfo, '站点配置调试信息', {
    confirmButtonText: '确定',
    type: 'info',
    customStyle: {
      width: '600px'
    }
  })

  // 同时在控制台输出详细信息
  siteConfigs.value.forEach((config, index) => {
    console.log(`站点 ${index + 1}: ${config.site_name}`)
    console.log(`  分类: ${config.blog_category_name} (ID: ${config.blog_category_id})`)
    console.log(`  标签: ${config.blog_tags?.length || 0} 个`, config.blog_tags)
    console.log(`  关键词:`, config.keywords)
    console.log(`  完整配置:`, config)
  })
}

// 生成编辑站点配置
const generateEditSiteConfigs = async () => {
  if (!editForm.selected_sites || editForm.selected_sites.length === 0) {
    ElMessage.warning('请先选择站点')
    return
  }

  editGenerating.value = true
  try {
    console.log('=== 开始生成编辑站点配置 ===')

    // 获取关键词 - 使用与新建模式一致的逻辑
    let keywords = []
    if (editForm.keyword_source === 'custom') {
      if (!editForm.custom_keywords || editForm.custom_keywords.trim() === '') {
        ElMessage.warning('请输入关键词')
        return
      }
      keywords = editForm.custom_keywords.split(',').map(k => k.trim()).filter(k => k)
      console.log(`解析出 ${keywords.length} 个自定义关键词:`, keywords)
    } else if (editForm.use_keyword_strategy) {
      console.log('使用智能选词策略')
      // 策略选词：生成配置时不需要预先获取关键词，每次任务执行时动态选择
      keywords = ['[智能策略选词]'] // 占位符，表示使用策略选词
      console.log('智能策略选词模式：将在任务执行时动态选择关键词')
    } else if (editForm.keyword_source === 'library') {
      console.log('从词库获取关键词，分类:', editForm.keyword_categories)

      if (!editForm.keyword_categories || editForm.keyword_categories.length === 0) {
        ElMessage.warning('请先选择关键词分类')
        return
      }

      // 判断是否使用智能推荐
      const useSmartRecommendation = editForm.keyword_selection_strategy === 'random_one'

      if (useSmartRecommendation) {
        // 智能推荐：使用占位符，实际关键词在任务执行时动态选择
        keywords = ['[智能推荐选词]']
        console.log('智能推荐选词模式：将在任务执行时动态选择关键词')
      } else {
        // 固定选词：从词库API获取关键词（这里简化处理）
        keywords = ['[从词库选择]'] // 占位符，表示从词库选择
        console.log('从词库选择模式：显示占位符')
      }
    }

    if (keywords.length === 0) {
      console.log('⚠️ 没有可用的关键词，但仍需要生成站点配置')
      // 不要提前返回，继续处理站点配置生成，只是关键词为空
      keywords = ['[请配置关键词]'] // 使用占位符
    }

    // 获取选中站点的详细信息
    const sitePromises = editForm.selected_sites.map(siteId =>
      apiClient.get(`/v1/wordpress-site/${siteId}`)
    )

    const siteResponses = await Promise.all(sitePromises)
    const sites = siteResponses.map(response => response.data || response)

    // 为每个站点生成配置 - 使用与新建模式一致的逻辑
    const configs = []
    for (const site of sites) {
      console.log(`\n处理站点: ${site.name} (ID: ${site.id})`)

      const siteKeywords = []

      // 判断是否使用动态选词 - 但自定义关键词优先级最高
      const useSmartStrategy = editForm.use_keyword_strategy && editForm.keyword_source !== 'custom' // 智能筛选策略，但自定义关键词时不使用
      const useSmartRecommendation = editForm.keyword_source === 'library' &&
                                     editForm.keyword_selection_strategy === 'random_one' // 智能推荐

      if (useSmartStrategy || useSmartRecommendation) {
        // 策略选词或智能推荐：使用占位符，实际关键词在任务执行时动态选择
        let strategyDesc = ''
        if (useSmartStrategy && useSmartRecommendation) {
          strategyDesc = '智能策略+推荐选词'
        } else if (useSmartStrategy) {
          strategyDesc = '智能策略选词'
        } else {
          strategyDesc = '智能推荐选词'
        }
        siteKeywords.push(`[${strategyDesc}]`)
        console.log(`  + ${strategyDesc}模式：将在任务执行时动态选择关键词`)
      } else {
        // 根据关键词来源决定分配数量
        const keywordsPerSite = editForm.keyword_source === 'library'
          ? Math.min(1, keywords.length) // 词库分类：每个站点只分配1个关键词
          : Math.min(3, keywords.length) // 自定义关键词：每个站点最多3个关键词

        console.log(`  关键词来源: ${editForm.keyword_source}, 分配数量: ${keywordsPerSite}`)

        for (let i = 0; i < keywordsPerSite; i++) {
          if (editForm.keyword_source === 'custom') {
            // 自定义关键词：按顺序分配
            if (i < keywords.length) {
              siteKeywords.push(keywords[i])
              console.log(`  + 分配关键词 ${i + 1}: "${keywords[i]}"`)
            }
          } else {
            // 词库选择：使用占位符
            siteKeywords.push(keywords[0] || '[从词库选择]')
            console.log(`  + 分配关键词 ${i + 1}: "${keywords[0] || '[从词库选择]'}"`)
            break // 词库模式只分配一个
          }
        }
      }

      // 获取默认分类
      let defaultCategoryId = null
      let defaultCategoryName = '未分类'
      if (site.blog_categories && site.blog_categories.length > 0) {
        const defaultCategory = site.blog_categories.find(cat => cat.name === '未分类') || site.blog_categories[0]
        defaultCategoryId = defaultCategory.id
        defaultCategoryName = defaultCategory.name
        console.log(`  + 默认博客分类: ${defaultCategoryName} (ID: ${defaultCategoryId})`)
      } else {
        console.log(`  ! 该站点没有博客分类`)
      }

      // 获取博客标签选项
      const blogTagsOptions = site.blog_tags || []

      const config = {
        site_id: site.id,
        site_name: site.name,
        site_url: site.url,
        keywords: siteKeywords,
        blog_category_id: defaultCategoryId,
        blog_category_name: defaultCategoryName,
        blog_categories: site.blog_categories || [],
        blog_tags: [], // 默认不选择标签
        blog_tags_options: blogTagsOptions,
        filtered_blog_tags_options: blogTagsOptions,
        tagsLoading: false
      }

      configs.push(config)
      console.log(`  ✓ 站点配置完成: ${siteKeywords.length} 个关键词`)
    }

    siteConfigs.value = configs // 修复：使用 siteConfigs 而不是 editSiteConfigs
    console.log('=== 编辑站点配置生成完成 ===')
    console.log(`成功生成 ${configs.length} 个站点配置`)
  } catch (error) {
    console.error('生成编辑站点配置失败:', error)
    ElMessage.error('生成站点配置失败')
  } finally {
    editGenerating.value = false
  }
}

// 编辑站点配置标签过滤
const filterEditSiteConfigTags = (configIndex, query) => {
  const config = siteConfigs.value[configIndex] // 修复：使用 siteConfigs 而不是 editSiteConfigs
  if (!config) return

  if (!query) {
    config.filtered_blog_tags_options = config.blog_tags_options
  } else {
    config.filtered_blog_tags_options = config.blog_tags_options.filter(tag =>
      tag.name.toLowerCase().includes(query.toLowerCase())
    )
  }
}

// 编辑站点配置标签选择框显示状态变化
const handleEditTagSelectVisibleChange = (visible, configIndex) => {
  if (visible) {
    const config = siteConfigs.value[configIndex] // 修复：使用 siteConfigs 而不是 editSiteConfigs
    if (config) {
      config.filtered_blog_tags_options = config.blog_tags_options
    }
  }
}

// 防抖自动刷新编辑站点配置
const debouncedRefreshEditSiteConfigs = () => {
  // 清除之前的定时器
  if (editAutoRefreshTimer.value) {
    clearTimeout(editAutoRefreshTimer.value)
  }

  // 设置新的定时器
  editAutoRefreshTimer.value = setTimeout(async () => {
    if (editDialogVisible.value && editForm.selected_sites && editForm.selected_sites.length > 0) {
      console.log('🔄 左侧配置变化，自动刷新右侧站点配置')
      await autoRefreshEditSiteConfigs()
    }
  }, 800) // 800ms防抖延迟
}

// 自动刷新编辑站点配置
const autoRefreshEditSiteConfigs = async () => {
  if (editGenerating.value) {
    console.log('⏳ 正在生成配置中，跳过自动刷新')
    return
  }

  try {
    editGenerating.value = true
    console.log('=== 开始自动刷新编辑站点配置 ===')

    // 保存当前的站点配置状态
    const currentConfigs = {}
    siteConfigs.value.forEach(config => {
      currentConfigs[config.site_id] = {
        blog_category_id: config.blog_category_id,
        blog_tags: [...(config.blog_tags || [])]
      }
    })

    // 获取新的关键词 - 使用与新建模式一致的逻辑
    let keywords = []
    if (editForm.keyword_source === 'custom') {
      keywords = editForm.custom_keywords ? editForm.custom_keywords.split(',').map(k => k.trim()).filter(k => k) : []
      console.log(`解析出 ${keywords.length} 个自定义关键词:`, keywords)
    } else if (editForm.keyword_source === 'library') {
      console.log('从词库获取关键词，分类:', editForm.keyword_categories)

      if (!editForm.keyword_categories || editForm.keyword_categories.length === 0) {
        console.log('⚠️ 没有选择关键词分类，保持现有配置')
        return
      }

      // 判断是否使用智能推荐
      const useSmartRecommendation = editForm.keyword_selection_strategy === 'random_one'

      if (useSmartRecommendation) {
        // 智能推荐：使用占位符
        keywords = ['[智能推荐选词]']
        console.log('智能推荐选词模式：将在任务执行时动态选择关键词')
      } else {
        // 固定选词：从词库选择
        keywords = ['[从词库选择]']
        console.log('从词库选择模式：显示占位符')
      }
    } else if (editForm.use_keyword_strategy) {
      console.log('使用智能选词策略')
      keywords = ['[智能策略选词]'] // 占位符，表示使用策略选词
    }

    if (keywords.length === 0) {
      console.log('⚠️ 没有可用的关键词，但仍需要更新站点配置以反映站点选择的变化')
      // 不要提前返回，继续处理站点配置更新，只是关键词为空
      keywords = ['[请配置关键词]'] // 使用占位符
    }

    // 获取选中站点的详细信息
    const sitePromises = editForm.selected_sites.map(siteId =>
      apiClient.get(`/v1/wordpress-site/${siteId}`)
    )

    const siteResponses = await Promise.all(sitePromises)
    const sites = siteResponses.map(response => response.data || response)

    // 更新站点配置
    const newConfigs = []
    for (const site of sites) {
      const currentConfig = currentConfigs[site.id] || {}

      // 重新分配关键词 - 使用与新建模式一致的逻辑
      const siteKeywords = []

      // 判断是否使用动态选词 - 但自定义关键词优先级最高
      const useSmartStrategy = editForm.use_keyword_strategy && editForm.keyword_source !== 'custom' // 智能筛选策略，但自定义关键词时不使用
      const useSmartRecommendation = editForm.keyword_source === 'library' &&
                                     editForm.keyword_selection_strategy === 'random_one' // 智能推荐

      if (useSmartStrategy || useSmartRecommendation) {
        // 策略选词或智能推荐：使用占位符
        let strategyDesc = ''
        if (useSmartStrategy && useSmartRecommendation) {
          strategyDesc = '智能策略+推荐选词'
        } else if (useSmartStrategy) {
          strategyDesc = '智能策略选词'
        } else {
          strategyDesc = '智能推荐选词'
        }
        siteKeywords.push(`[${strategyDesc}]`)
      } else {
        // 根据关键词来源决定分配数量
        const keywordsPerSite = editForm.keyword_source === 'library'
          ? Math.min(1, keywords.length) // 词库分类：每个站点只分配1个关键词
          : Math.min(3, keywords.length) // 自定义关键词：每个站点最多3个关键词

        for (let i = 0; i < keywordsPerSite; i++) {
          if (editForm.keyword_source === 'custom') {
            // 自定义关键词：按顺序分配
            if (i < keywords.length) {
              siteKeywords.push(keywords[i])
            }
          } else {
            // 词库选择：使用占位符
            siteKeywords.push(keywords[0] || '[从词库选择]')
            break // 词库模式只分配一个
          }
        }
      }

      // 保持现有的博客分类和标签选择
      let blogCategoryId = currentConfig.blog_category_id || null
      let blogCategoryName = '未分类'

      // 如果没有现有配置，使用默认分类
      if (!blogCategoryId && site.blog_categories && site.blog_categories.length > 0) {
        const defaultCategory = site.blog_categories.find(cat => cat.name === '未分类') || site.blog_categories[0]
        blogCategoryId = defaultCategory.id
        blogCategoryName = defaultCategory.name
      } else if (blogCategoryId) {
        // 查找分类名称
        const category = site.blog_categories?.find(cat => cat.id === blogCategoryId)
        blogCategoryName = category ? category.name : '未分类'
      }

      const config = {
        site_id: site.id,
        site_name: site.name,
        site_url: site.url,
        keywords: siteKeywords, // 使用新的关键词
        blog_category_id: blogCategoryId, // 保持现有选择
        blog_category_name: blogCategoryName,
        blog_categories: site.blog_categories || [],
        blog_tags: currentConfig.blog_tags || [], // 保持现有选择
        blog_tags_options: site.blog_tags || [],
        filtered_blog_tags_options: site.blog_tags || [],
        tagsLoading: false
      }

      newConfigs.push(config)
    }

    siteConfigs.value = newConfigs // 修复：使用 siteConfigs 而不是 editSiteConfigs
    console.log('=== 自动刷新编辑站点配置完成 ===')
    console.log(`✅ 更新了 ${newConfigs.length} 个站点配置，保持了用户的分类和标签选择`)
  } catch (error) {
    console.error('自动刷新编辑站点配置失败:', error)
  } finally {
    editGenerating.value = false
  }
}

// 加载编辑站点配置（复用新建时的配置系统）
const loadEditSiteConfigs = async (plan, selectedSiteIds) => {
  try {
    console.log('=== 开始加载编辑站点配置 ===')
    console.log('计划ID:', plan.id)
    console.log('选中站点:', selectedSiteIds)

    siteConfigs.value = [] // 复用 siteConfigs 而不是 editSiteConfigs

    if (!selectedSiteIds || selectedSiteIds.length === 0) {
      console.log('没有选中的站点，跳过配置加载')
      return
    }

    // 获取选中站点的详细信息
    const sitePromises = selectedSiteIds.map(siteId =>
      apiClient.get(`/v1/wordpress-site/${siteId}`)
    )

    const siteResponses = await Promise.all(sitePromises)
    const sites = siteResponses.map(response => response.data || response)

    // 解析保存的站点配置（这是关键！）
    console.log('原始 plan.site_configs:', plan.site_configs)
    console.log('plan.site_configs 类型:', typeof plan.site_configs)

    const savedSiteConfigs = {}
    if (plan.site_configs) {
      try {
        const configs = typeof plan.site_configs === 'string' ? JSON.parse(plan.site_configs) : plan.site_configs
        console.log('解析到的保存配置:', configs)
        console.log('配置数组长度:', Array.isArray(configs) ? configs.length : '不是数组')

        if (Array.isArray(configs)) {
          configs.forEach((config, index) => {
            savedSiteConfigs[config.site_id] = config
            console.log(`配置 ${index + 1} - 站点 ${config.site_id}:`, {
              site_name: config.site_name,
              blog_category_id: config.blog_category_id,
              blog_category_name: config.blog_category_name,
              blog_tags: config.blog_tags,
              keywords: config.keywords
            })
          })
        }
      } catch (error) {
        console.error('解析保存的站点配置失败:', error)
        console.error('原始数据:', plan.site_configs)
      }
    } else {
      console.log('计划中没有保存的站点配置')
    }

    // 为每个站点创建配置
    const configs = []
    for (const site of sites) {
      const savedConfig = savedSiteConfigs[site.id] || {}

      console.log(`\n处理站点: ${site.name} (ID: ${site.id})`)
      console.log('保存的配置:', savedConfig)

      // 获取关键词 - 优先使用保存的配置
      let siteKeywords = []
      if (savedConfig.keywords && Array.isArray(savedConfig.keywords)) {
        siteKeywords = savedConfig.keywords
        console.log(`  使用保存的关键词: ${siteKeywords.length} 个`, siteKeywords)
      } else {
        // 如果没有保存的关键词，根据计划的关键词来源生成
        if (plan.keyword_source === 'custom' && plan.keywords) {
          const planKeywords = plan.keywords.split(',').map(k => k.trim()).filter(k => k)
          siteKeywords = planKeywords.slice(0, Math.min(planKeywords.length, 3))
        } else {
          siteKeywords = ['[动态选词]'] // 占位符
        }
        console.log(`  生成新关键词: ${siteKeywords.length} 个`, siteKeywords)
      }

      // 获取博客分类 - 优先使用保存的配置
      let blogCategoryId = savedConfig.blog_category_id || null
      let blogCategoryName = savedConfig.blog_category_name || '未分类'

      // 如果没有保存的分类配置，使用默认分类
      if (!blogCategoryId && site.blog_categories && site.blog_categories.length > 0) {
        const defaultCategory = site.blog_categories.find(cat => cat.name === '未分类') || site.blog_categories[0]
        blogCategoryId = defaultCategory.id
        blogCategoryName = defaultCategory.name
        console.log(`  使用默认分类: ${blogCategoryName} (ID: ${blogCategoryId})`)
      } else {
        console.log(`  使用保存的分类: ${blogCategoryName} (ID: ${blogCategoryId})`)
      }

      // 获取博客标签 - 优先使用保存的配置
      let blogTags = []
      if (savedConfig.blog_tags && Array.isArray(savedConfig.blog_tags)) {
        // 确保标签ID是数字类型
        blogTags = savedConfig.blog_tags
          .map(tag => typeof tag === 'number' ? tag : parseInt(tag))
          .filter(id => !isNaN(id))
        console.log(`  使用保存的标签: ${blogTags.length} 个`, blogTags)
      } else {
        console.log(`  没有保存的标签配置`)
      }

      const blogTagsOptions = site.blog_tags || []

      const config = {
        site_id: site.id,
        site_name: site.name,
        site_url: site.url,
        keywords: siteKeywords,
        blog_category_id: blogCategoryId,
        blog_category_name: blogCategoryName,
        blog_categories: site.blog_categories || [],
        blog_tags: blogTags,
        blog_tags_options: blogTagsOptions,
        filtered_blog_tags_options: blogTagsOptions,
        tagsLoading: false
      }

      configs.push(config)
      console.log(`  ✓ 站点配置创建完成`)
    }

    siteConfigs.value = configs // 复用 siteConfigs
    console.log('=== 编辑站点配置加载完成 ===')
    console.log(`成功加载 ${configs.length} 个站点配置`)
  } catch (error) {
    console.error('加载编辑站点配置失败:', error)
    siteConfigs.value = [] // 复用 siteConfigs
  }
}

// 解析博客标签显示
const parseTagsDisplay = (tags) => {
  if (!tags) return []

  // 如果是字符串（逗号分隔的ID），需要查找对应的标签名称
  if (typeof tags === 'string') {
    return tags.split(',').filter(tag => tag.trim())
  }

  // 如果是数组，可能包含标签对象或标签名称
  if (Array.isArray(tags)) {
    return tags.map(tag => {
      if (typeof tag === 'object' && tag.name) {
        return tag.name
      }
      return tag.toString()
    })
  }

  return []
}

// 加载关键词分类
const loadKeywordCategories = async () => {
  try {
    const response = await apiClient.get('/v1/keyword-library/categories')
    keywordCategories.value = response || []
    console.log(`加载了 ${keywordCategories.value.length} 个关键词分类`)
  } catch (error) {
    console.error('加载关键词分类失败:', error)
    keywordCategories.value = []
  }
}

// 加载站点分类
const loadSiteCategories = async () => {
  try {
    const response = await apiClient.get('/v1/wordpress-site/categories/list')
    siteCategories.value = response?.categories || []
    console.log(`加载了 ${siteCategories.value.length} 个站点分类`)
  } catch (error) {
    console.error('加载站点分类失败:', error)
    siteCategories.value = []
  }
}

// 加载所有站点
const loadAllSites = async () => {
  try {
    const response = await apiClient.get('/v1/wordpress-site/', {
      params: { size: 100, is_active: true }
    })
    allSites.value = response?.items || []
    console.log(`加载了 ${allSites.value.length} 个活跃站点`)

    // 构建树形数据
    buildSiteTreeData()
  } catch (error) {
    console.error('加载站点失败:', error)
    allSites.value = []
  }
}

// 构建站点树形数据
const buildSiteTreeData = () => {
  const categoryMap = new Map()

  // 按分类分组站点
  allSites.value.forEach(site => {
    const category = site.category || '未分类'
    if (!categoryMap.has(category)) {
      categoryMap.set(category, [])
    }
    categoryMap.get(category).push({
      id: `site-${site.id}`,
      label: site.name,
      url: site.url,
      type: 'site',
      siteId: site.id,
      category: category
    })
  })

  // 构建树形结构
  const treeData = []
  categoryMap.forEach((sites, category) => {
    treeData.push({
      id: `category-${category}`,
      label: category,
      type: 'category',
      category: category,
      children: sites
    })
  })

  siteTreeData.value = treeData
  console.log('构建站点树形数据:', treeData)
}

// 加载站点分类计数
const loadSiteCategoryCounts = async () => {
  try {
    // 获取所有活跃站点
    let page = 1
    const size = 100
    let allSites = []
    let hasMore = true

    // 分页获取所有站点
    while (hasMore) {
      const response = await apiClient.get('/v1/wordpress-site/', {
        params: {
          page,
          size,
          is_active: true
        }
      })

      if (response.items && response.items.length > 0) {
        allSites = allSites.concat(response.items)
      }

      // 检查是否还有更多数据
      hasMore = response.items && response.items.length === size &&
                response.total > page * size
      page++
    }

    // 计算分类计数
    const counts = {}
    allSites.forEach(site => {
      const siteCategory = site.category || 'uncategorized'
      counts[siteCategory] = (counts[siteCategory] || 0) + 1
    })

    siteCategoryCounts.value = counts
    console.log('站点分类计数:', counts)
  } catch (error) {
    console.error('加载站点分类计数失败:', error)
    siteCategoryCounts.value = {}
  }
}

// 处理表格选择
const handleTableSelectionChange = (selection) => {
  selectedConfigs.value = selection.map(item => siteConfigs.value.indexOf(item))
}

// 站点配置标签过滤方法
const filterSiteConfigTags = (configIndex, query) => {
  const config = siteConfigs.value[configIndex]
  if (!config) return
  
  if (!query || !config.blog_tags_options) {
    config.filtered_blog_tags_options = config.blog_tags_options || []
    return
  }
  
  // 过滤现有标签
  config.filtered_blog_tags_options = config.blog_tags_options.filter(tag => 
    tag.name.toLowerCase().includes(query.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(query.toLowerCase()))
  )
}

// 创建新标签的方法（用于定时任务）
const createNewTagsForSchedule = async (newTagNames, siteId) => {
  const createdTags = []
  
  for (const tagName of newTagNames) {
    try {
      const response = await apiClient.post(`/v1/wordpress-site/${siteId}/tags/create`, {
        name: tagName,
        description: `通过定时任务自动创建的标签`
      })
      
      if (response.success && response.data) {
        createdTags.push(response.data)
        ElMessage.success(`标签 "${tagName}" 创建成功`)
      }
    } catch (error) {
      console.error(`创建标签 "${tagName}" 失败:`, error)
      ElMessage.error(`创建标签 "${tagName}" 失败: ${error.response?.data?.error || error.message}`)
      throw error
    }
  }
  
  return createdTags
}

// 策略对话框相关方法
const openStrategyDialog = () => {
  // 设置策略分类为当前选择的分类
  strategyForm.strategy_categories = [...scheduleForm.keyword_categories]
  strategyDialogVisible.value = true
  // 重置分页状态
  previewCurrentPage.value = 1
  previewPageSize.value = 20
  previewTotal.value = 0
  // 清空之前的预览数据
  strategyPreviewData.value = {
    total_count: 0,
    preview_keywords: []
  }
  // 自动预览一次（延迟执行，确保对话框已打开）
  setTimeout(() => {
    refreshPreview()
  }, 100)
}

const handleStrategyDialogClose = () => {
  strategyDialogVisible.value = false
}

const openEditStrategyDialog = () => {
  // 在编辑模式下打开策略对话框
  // 将编辑表单的策略配置同步到策略表单
  strategyForm.strategy_categories = [...editForm.keyword_categories]
  strategyForm.strategy_intent = editForm.strategy_intent || ''
  strategyForm.strategy_volume_min = editForm.strategy_volume_min || null
  strategyForm.strategy_volume_max = editForm.strategy_volume_max || null
  strategyForm.strategy_difficulty_min = editForm.strategy_difficulty_min || 1
  strategyForm.strategy_difficulty_max = editForm.strategy_difficulty_max || 100
  strategyForm.strategy_cpc_min = editForm.strategy_cpc_min || null
  strategyForm.strategy_cpc_max = editForm.strategy_cpc_max || null
  strategyForm.strategy_competitive_density_min = editForm.strategy_competitive_density_min || 0.00
  strategyForm.strategy_competitive_density_max = editForm.strategy_competitive_density_max || 1.00
  strategyForm.strategy_countries = editForm.strategy_countries ? [...editForm.strategy_countries] : []

  // 同步滑块范围值 - 确保使用实际值而不是默认值
  const difficultyMin = editForm.strategy_difficulty_min !== null ? editForm.strategy_difficulty_min : 1
  const difficultyMax = editForm.strategy_difficulty_max !== null ? editForm.strategy_difficulty_max : 100
  const densityMin = editForm.strategy_competitive_density_min !== null ? editForm.strategy_competitive_density_min : 0.00
  const densityMax = editForm.strategy_competitive_density_max !== null ? editForm.strategy_competitive_density_max : 1.00

  strategyForm.difficulty_range = [difficultyMin, difficultyMax]
  strategyForm.competitive_density_range = [densityMin, densityMax]

  console.log('编辑模式策略配置同步:', {
    intent: editForm.strategy_intent,
    volume_min: editForm.strategy_volume_min,
    volume_max: editForm.strategy_volume_max,
    difficulty_min: editForm.strategy_difficulty_min,
    difficulty_max: editForm.strategy_difficulty_max,
    cpc_min: editForm.strategy_cpc_min,
    cpc_max: editForm.strategy_cpc_max,
    density_min: editForm.strategy_competitive_density_min,
    density_max: editForm.strategy_competitive_density_max,
    countries: editForm.strategy_countries,
    categories: editForm.strategy_categories
  })

  strategyDialogVisible.value = true
  // 重置分页状态
  previewCurrentPage.value = 1
  previewPageSize.value = 20
  previewTotal.value = 0
  // 清空之前的预览数据
  strategyPreviewData.value = {
    total_count: 0,
    preview_keywords: []
  }
  // 自动预览一次（延迟执行，确保对话框已打开）
  setTimeout(() => {
    refreshPreview()
  }, 100)
}

const onStrategyChange = () => {
  // 策略配置改变时自动刷新预览（防抖）
  clearTimeout(window.strategyChangeTimer)
  window.strategyChangeTimer = setTimeout(() => {
    // 重置到第一页
    previewCurrentPage.value = 1
    refreshPreview()
  }, 800) // 增加防抖时间到800ms，避免频繁调用
}

const onDifficultyRangeChange = (value) => {
  strategyForm.strategy_difficulty_min = value[0]
  strategyForm.strategy_difficulty_max = value[1]
  onStrategyChange()
}

const onCompetitiveDensityRangeChange = (value) => {
  strategyForm.strategy_competitive_density_min = value[0]
  strategyForm.strategy_competitive_density_max = value[1]
  onStrategyChange()
}

const refreshPreview = async () => {
  previewLoading.value = true
  try {
    const strategyData = {
      strategy_intent: strategyForm.strategy_intent,
      strategy_volume_min: strategyForm.strategy_volume_min,
      strategy_volume_max: strategyForm.strategy_volume_max,
      strategy_difficulty_min: strategyForm.strategy_difficulty_min,
      strategy_difficulty_max: strategyForm.strategy_difficulty_max,
      strategy_cpc_min: strategyForm.strategy_cpc_min,
      strategy_cpc_max: strategyForm.strategy_cpc_max,
      strategy_competitive_density_min: strategyForm.strategy_competitive_density_min,
      strategy_competitive_density_max: strategyForm.strategy_competitive_density_max,
      strategy_trend_direction: strategyForm.strategy_trend_direction,
      strategy_results_min: strategyForm.strategy_results_min,
      strategy_results_max: strategyForm.strategy_results_max,
      strategy_countries: strategyForm.strategy_countries,
      strategy_categories: strategyForm.strategy_categories
    }

    const response = await apiClient.post('/v1/scheduled-publish/preview-strategy-keywords', strategyData, {
      params: {
        page: previewCurrentPage.value,
        page_size: previewPageSize.value
      }
    })

    strategyPreviewData.value = response
    previewTotal.value = response.total_count
  } catch (error) {
    console.error('预览策略关键词失败:', error)
    ElMessage.error('预览策略关键词失败')
  } finally {
    previewLoading.value = false
  }
}

const clearStrategyForm = () => {
  strategyForm.strategy_intent = ''
  strategyForm.strategy_volume_min = null
  strategyForm.strategy_volume_max = null
  strategyForm.difficulty_range = [1, 100]
  strategyForm.strategy_difficulty_min = 1
  strategyForm.strategy_difficulty_max = 100
  strategyForm.strategy_cpc_min = null
  strategyForm.strategy_cpc_max = null
  strategyForm.competitive_density_range = [0.00, 1.00]
  strategyForm.strategy_competitive_density_min = 0.00
  strategyForm.strategy_competitive_density_max = 1.00
  strategyForm.strategy_trend_direction = ''
  strategyForm.strategy_results_min = null
  strategyForm.strategy_results_max = null
  strategyForm.strategy_countries = []
  // 不清空分类，保持当前选择的分类
  // 重置到第一页并刷新预览
  previewCurrentPage.value = 1
  refreshPreview()
}

// 分页事件处理
const handlePreviewSizeChange = (newSize) => {
  previewPageSize.value = newSize
  previewCurrentPage.value = 1 // 重置到第一页
  refreshPreview()
}

const handlePreviewCurrentChange = (newPage) => {
  previewCurrentPage.value = newPage
  refreshPreview()
}

const applyStrategy = () => {
  // 判断当前是否在编辑模式
  const isEditMode = editDialogVisible.value
  const targetForm = isEditMode ? editForm : scheduleForm

  // 将策略配置应用到目标表单
  targetForm.use_keyword_strategy = true
  targetForm.strategy_intent = strategyForm.strategy_intent
  targetForm.strategy_volume_min = strategyForm.strategy_volume_min
  targetForm.strategy_volume_max = strategyForm.strategy_volume_max
  targetForm.strategy_difficulty_min = strategyForm.strategy_difficulty_min
  targetForm.strategy_difficulty_max = strategyForm.strategy_difficulty_max
  targetForm.strategy_cpc_min = strategyForm.strategy_cpc_min
  targetForm.strategy_cpc_max = strategyForm.strategy_cpc_max
  targetForm.strategy_competitive_density_min = strategyForm.strategy_competitive_density_min
  targetForm.strategy_competitive_density_max = strategyForm.strategy_competitive_density_max
  targetForm.strategy_trend_direction = strategyForm.strategy_trend_direction
  targetForm.strategy_results_min = strategyForm.strategy_results_min
  targetForm.strategy_results_max = strategyForm.strategy_results_max
  targetForm.strategy_countries = [...strategyForm.strategy_countries]
  targetForm.strategy_categories = [...strategyForm.strategy_categories]

  strategyDialogVisible.value = false
  ElMessage.success('智能选词策略已应用')
}

const clearStrategyConfig = () => {
  scheduleForm.use_keyword_strategy = false
  scheduleForm.strategy_intent = ''
  scheduleForm.strategy_volume_min = null
  scheduleForm.strategy_volume_max = null
  scheduleForm.strategy_difficulty_min = null
  scheduleForm.strategy_difficulty_max = null
  scheduleForm.strategy_cpc_min = null
  scheduleForm.strategy_cpc_max = null
  scheduleForm.strategy_competitive_density_min = null
  scheduleForm.strategy_competitive_density_max = null
  scheduleForm.strategy_trend_direction = ''
  scheduleForm.strategy_results_min = null
  scheduleForm.strategy_results_max = null
  scheduleForm.strategy_countries = []
  scheduleForm.strategy_categories = []
}

// 预览相关方法
const formatNumberPreview = (num) => {
  if (num === null || num === undefined) return '--'

  const number = parseInt(num)
  if (number >= 1000000000) {
    return (number / 1000000000).toFixed(1) + 'B'
  } else if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M'
  } else if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K'
  } else {
    return number.toLocaleString()
  }
}

const getTrendDirectionPreview = (trendData) => {
  try {
    const trends = JSON.parse(trendData)
    if (Array.isArray(trends) && trends.length > 0) {
      const first = trends[0]
      const last = trends[trends.length - 1]
      return last > first ? 'trend-up' : 'trend-down'
    }
  } catch (error) {
    console.error('解析趋势数据失败:', error)
  }
  return 'trend-neutral'
}

const getTrendPointsPreview = (trendData) => {
  try {
    const trends = JSON.parse(trendData)
    if (Array.isArray(trends) && trends.length > 0) {
      const min = Math.min(...trends)
      const max = Math.max(...trends)
      const range = max - min || 1

      // 将数据标准化为0-100的百分比
      return trends.map(value => {
        return Math.round(((value - min) / range) * 80 + 10) // 10-90%的范围
      })
    }
  } catch (error) {
    console.error('解析趋势数据失败:', error)
  }
  return []
}

const getTrendSummaryPreview = (trendData) => {
  try {
    const trends = JSON.parse(trendData)
    if (Array.isArray(trends) && trends.length > 0) {
      const first = trends[0]
      const last = trends[trends.length - 1]
      const change = ((last - first) / first * 100).toFixed(1)
      return change > 0 ? `+${change}%` : `${change}%`
    }
  } catch (error) {
    console.error('解析趋势数据失败:', error)
  }
  return '--'
}
</script>

<style scoped>
.strategy-config {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.strategy-config .el-form-item {
  margin-bottom: 16px;
}

.strategy-preview-content {
  max-height: 500px;
  overflow-y: auto;
}

.strategy-preview-content .el-table {
  font-size: 13px;
}

/* 策略对话框样式 */
.strategy-dialog-content {
  display: flex;
  gap: 20px;
  height: 600px;
}

.strategy-config-panel {
  flex: 0 0 400px;
  overflow-y: auto;
}

.strategy-preview-panel {
  flex: 1;
  overflow: hidden;
}

.config-card, .preview-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-card .el-card__body,
.preview-card .el-card__body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.range-display {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.strategy-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.preview-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  padding: 8px 0;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #67c23a;
  margin-top: 8px;
  padding: 6px 12px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
}

.strategy-option {
  width: 100%;
  padding: 8px 0;
}

.strategy-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.strategy-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 使用与AI发布相同的样式 */
.scheduled-publish {
  position: fixed;
  left: 236px;
  top: 72px; 
  right: 0;
  bottom: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  transition: left 0.3s ease;
}

.scheduled-publish.sidebar-collapsed {
  left: 80px;
}

.main-content {
  display: flex;
  height: 100%;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.form-section {
  flex: 0 0 380px;
  min-width: 380px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.form-card, .list-card {
  height: fit-content;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px 24px;
}

.generate-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
  min-height: 0;
}

.form-buttons {
  flex-shrink: 0;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: space-between;
  height: 52px;
  align-items: center;
}

.form-button {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.form-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.35);
}

.form-buttons :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.form-buttons :deep(.el-button:not(.el-button--primary)) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.form-buttons :deep(.el-button:not(.el-button--primary):hover) {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.list-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  flex-shrink: 0; /* 头部不收缩 */
}

.list-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px 24px;
  min-height: 0; /* 允许内容区域收缩 */
}

.content-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.content-header .list-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.content-header .header-icon {
  font-size: 18px;
  color: white;
}

.header-stats {
  display: flex;
  gap: 24px;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.site-config-card {
  max-height: 400px;
  overflow-y: auto;
}

.form-header, .list-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: white;
}

.form-header .header-icon,
.list-header .header-icon {
  color: white;
  font-size: 18px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.keyword-input {
  width: 100%;
}

.site-option {
  padding: 4px 0;
}

.site-option .site-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.site-option .site-url {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.site-configs {
  max-height: 300px;
  overflow-y: auto;
}

.site-config-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
}

.site-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.site-url {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.config-details {
  margin-top: 12px;
}

.keywords-section, .category-section {
  margin-bottom: 8px;
}

.keywords-section label, .category-section label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.keyword-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.action-button {
  border-radius: 8px;
  font-weight: 600;
  padding: 12px 24px;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.success-button {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.secondary-button {
  background: #f0f2f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.list-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许内容区域收缩 */
}

.plan-table, .task-table {
  flex: 1;
  min-height: 0; /* 允许表格收缩 */
}

/* 移除原有的表格滚动样式，使用表格自身的滚动 */
.plan-table :deep(.el-table__body-wrapper) {
  /* 移除这里的样式，让表格自己管理滚动 */
}

/* 确保表格能够正确显示滚动条 */
.plan-table :deep(.el-table) {
  height: 100%;
}

.plan-table :deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

.plan-table :deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto;
}

.task-stats {
  font-weight: 600;
  color: #409eff;
}

.pagination-wrapper {
  padding: 16px 0;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
  flex-shrink: 0; /* 分页不收缩，确保固定在底部 */
  background: #fff;
}

.queue-status {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9ff;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.status-item .label {
  color: #606266;
  font-size: 13px;
}

.status-item .value {
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-section {
    flex: 0 0 360px;
    min-width: 360px;
  }
}

@media (max-width: 768px) {
  .scheduled-publish {
    width: calc(100vw - 16px);
    left: 8px;
    top: 72px;
    height: calc(100vh - 80px);
  }
  
  .main-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .form-section {
    flex: none;
    min-width: auto;
  }
  
  .list-section {
    flex: none;
    height: 60vh;
  }
}

/* 配置对话框样式 */
.config-dialog-content {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-summary {
  margin-bottom: 20px;
}

.config-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.config-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.selected-count {
  color: #909399;
  font-size: 14px;
}

.config-items {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.config-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.3s ease;
}

.config-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.config-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.config-item-header .site-info {
  flex: 1;
}

.config-item-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.config-item-header .site-url {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #909399;
}

.config-details {
  margin-top: 12px;
}

.keywords-section, .category-section {
  margin-bottom: 12px;
}

.keywords-section label, .category-section label {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.keyword-tag {
  margin: 0;
}

.dialog-actions {
  padding: 20px 0 0 0;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: auto;
}

/* 滚动条样式 */
.config-items::-webkit-scrollbar {
  width: 6px;
}

.config-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.config-items::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.config-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 频率详情样式 */
.frequency-details {
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  font-size: 14px;
  color: #303133;
}

.frequency-details .max-executions {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

/* 周几选择器样式 */
.el-checkbox-group .el-checkbox {
  margin-right: 12px;
  margin-bottom: 8px;
}

/* 自定义间隔输入样式 */
.el-input-number {
  width: 100%;
}

/* 时间选择器样式 */
.el-time-picker {
  width: 100%;
}

.form-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.list-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.category-section {
  margin-bottom: 12px;
}

.category-section label {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  display: block;
}

.tags-section {
  margin-bottom: 12px;
}

.tags-section label {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  display: block;
}

.tag-option {
  padding: 4px 0;
}

.tag-option .tag-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.tag-option .tag-info {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.tag-option .tag-count {
  color: #67C23A;
}

.tag-option .tag-desc {
  color: #909399;
  font-size: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.article-title {
  font-weight: 500;
  color: #409eff;
  cursor: default;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
  display: inline-block;
}

.text-muted {
  color: #909399;
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin: 0;
}

/* 编辑对话框样式 */
.edit-dialog-content {
  display: flex;
  gap: 20px;
  height: 600px;
}

.edit-left-panel {
  flex: 0 0 400px;
  border-right: 1px solid #e4e7ed;
  padding-right: 20px;
}

.edit-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.edit-form {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.empty-config {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.site-configs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.config-summary {
  margin-bottom: 12px;
}

.config-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  flex: 1;
}

.config-table {
  width: 100%;
}

.edit-config-table {
  height: 100%;
}

.keywords-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  align-items: center;
}

.more-keywords {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.tag-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tag-name {
  font-weight: 500;
}

.tag-info {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.tag-count {
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.config-table .el-table__header-wrapper {
  background-color: #fafafa;
}

.site-info-compact {
  display: flex;
  flex-direction: column;
}

.site-info-compact .site-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.site-info-compact .site-url {
  font-size: 12px;
  color: #909399;
}

.keywords-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.keyword-tag-small {
  margin: 0;
  font-size: 12px;
}

.tag-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tag-name {
  font-weight: 500;
}

.tag-count {
  font-size: 12px;
  color: #909399;
}

/* 站点选项样式 */
.site-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.site-option .site-name {
  font-weight: 500;
  color: #303133;
}

.site-option .site-url {
  font-size: 12px;
  color: #909399;
}

/* 操作按钮两排布局 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
}

.button-row {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  width: fit-content;
}

.button-row .el-button {
  margin: 0 !important;
  width: 50px;
  padding: 4px 8px;
}

.button-row .el-button span {
  font-size: 12px;
}

/* 确保操作列单元格居中 */
.plan-table .el-table__cell:last-child {
  text-align: center !important;
}

.plan-table .el-table__cell:last-child .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 任务详情表格样式优化 */
.task-table {
  width: 100%;
}

.task-table .el-table__cell {
  padding: 8px 4px;
}

.task-table .article-title {
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-table .tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  max-height: 40px;
  overflow: hidden;
}

.task-table .tag-item {
  margin: 0;
  font-size: 11px;
  padding: 1px 4px;
}

/* 表单帮助文本样式 */
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 站点下拉树形选择样式 */
.site-tree-container {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.tree-header {
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 8px;
}

.tree-title {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.site-tree-dropdown {
  padding: 0 8px;
}

.tree-node {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
  color: #606266;
}

.node-label {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.node-count {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.node-details {
  margin-left: 24px;
  margin-top: 4px;
}

.site-url {
  font-size: 12px;
  color: #909399;
}

/* 简洁的选择提示 */
.selection-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #909399;
  line-height: 1;
}

/* 下拉框树形组件自定义样式 */
.site-tree-dropdown :deep(.el-tree-node__content) {
  height: auto;
  min-height: 32px;
  padding: 4px 0;
}

.site-tree-dropdown :deep(.el-tree-node__expand-icon) {
  color: #606266;
}

.site-tree-dropdown :deep(.el-tree-node__label) {
  flex: 1;
}

/* 下拉框弹出层样式 */
.site-select-dropdown {
  max-width: 500px !important;
}

.site-select-dropdown .el-select-dropdown__item {
  display: none;
}

/* 带帮助提示的标签样式 */
.label-with-help {
  display: inline-flex;
  align-items: center;
  cursor: help;
  transition: color 0.3s ease;
}

.label-with-help:hover {
  color: #409eff;
}

.label-with-help:hover .help-icon {
  color: #409eff;
}

/* 帮助图标样式 */
.help-icon {
  margin-left: 4px;
  color: #909399;
  cursor: help;
  font-size: 14px;
  transition: color 0.3s ease;
}

.help-icon:hover {
  color: #409eff;
}

/* 宽提示框样式 */
:deep(.wide-tooltip) {
  max-width: 400px !important;
  line-height: 1.5;
}

/* 预览表格中的趋势显示样式 */
.trend-display-preview {
  display: flex;
  align-items: center;
  gap: 6px;
}

.trend-chart-simple-preview {
  width: 40px;
  height: 20px;
  display: flex;
  align-items: end;
  gap: 1px;
  padding: 1px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.02);
}

.trend-point-preview {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.trend-up .trend-point-preview {
  background: linear-gradient(to top, #67C23A, #85ce61);
}

.trend-down .trend-point-preview {
  background: linear-gradient(to top, #F56C6C, #f78989);
}

.trend-neutral .trend-point-preview {
  background: linear-gradient(to top, #909399, #a6a9ad);
}

.trend-text-preview {
  font-size: 11px;
  font-weight: 500;
  color: #606266;
}
</style>