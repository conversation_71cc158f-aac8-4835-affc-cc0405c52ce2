-- 添加 execution_mode 字段到 scheduled_publish_plans 表
-- 执行日期: 2024-01-15
-- 描述: 为"仅执行一次"的任务添加执行模式选择（立即执行或定时执行）

-- 1. 添加 execution_mode 字段
ALTER TABLE scheduled_publish_plans 
ADD COLUMN execution_mode VARCHAR(20) NULL 
COMMENT '执行模式：immediate(立即执行) 或 scheduled(定时执行)';

-- 2. 为现有数据设置默认值
-- 对于 frequency_type = 'once' 的计划，设置为 'scheduled'（定时执行）
-- 对于其他频率类型，保持 NULL（不适用）
UPDATE scheduled_publish_plans 
SET execution_mode = 'scheduled' 
WHERE frequency_type = 'once' AND execution_mode IS NULL;

-- 3. 验证更新结果
SELECT 
    id,
    plan_name,
    frequency_type,
    execution_mode,
    scheduled_time,
    created_at
FROM scheduled_publish_plans 
ORDER BY created_at DESC 
LIMIT 10;

-- 4. 检查字段是否添加成功
DESCRIBE scheduled_publish_plans;

-- 或者使用以下查询检查字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'scheduled_publish_plans' 
    AND COLUMN_NAME = 'execution_mode';
