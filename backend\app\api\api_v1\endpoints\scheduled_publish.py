from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import random
import asyncio
import json

from ....db.session import get_db
from ....models.scheduled_publish import ScheduledPublishPlan, ScheduledPublishTask, TaskQueue
from ....models.wordpress_site import WordPressSite
from ....models.keyword_library import KeywordLibrary
from ....models.ai_config import AIConfig
from ....schemas.scheduled_publish import (
    ScheduledPublishPlanCreate,
    ScheduledPublishPlanUpdate,
    ScheduledPublishPlanResponse,
    ScheduledPublishPlanList,
    ScheduledPublishTaskResponse,
    ScheduledPublishTaskList,
    BatchTaskGenerateRequest,
    QueueStatusResponse,
    PlanStatistics,
    SiteConfigItem
)
from ....api.deps import get_current_user
from ....models.user import User
from ....services.scheduled_publish_service import ScheduledPublishService

router = APIRouter()


@router.get("/plans", response_model=ScheduledPublishPlanList)
async def get_scheduled_plans(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取定时发布计划列表"""
    query = db.query(ScheduledPublishPlan)
    
    if status == "active":
        query = query.filter(ScheduledPublishPlan.is_active == True)
    elif status == "inactive":
        query = query.filter(ScheduledPublishPlan.is_active == False)
    
    total = query.count()
    offset = (page - 1) * size
    items = query.order_by(ScheduledPublishPlan.created_at.desc()).offset(offset).limit(size).all()
    
    return ScheduledPublishPlanList(
        items=items,
        total=total,
        page=page,
        size=size
    )


@router.get("/plans/{plan_id}", response_model=ScheduledPublishPlanResponse)
async def get_scheduled_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个定时发布计划"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")
    return plan


@router.post("/plans", response_model=ScheduledPublishPlanResponse)
async def create_scheduled_plan(
    plan_data: ScheduledPublishPlanCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建定时发布计划"""
    import logging
    logger = logging.getLogger(__name__)

    # 调试时间接收
    logger.info(f"接收到的计划数据 - scheduled_time: {plan_data.scheduled_time} (类型: {type(plan_data.scheduled_time)})")
    if plan_data.end_time:
        logger.info(f"接收到的计划数据 - end_time: {plan_data.end_time} (类型: {type(plan_data.end_time)})")

    plan = ScheduledPublishPlan(**plan_data.dict())

    # 调试存储到数据库的时间
    logger.info(f"存储到数据库 - scheduled_time: {plan.scheduled_time} (类型: {type(plan.scheduled_time)})")
    if plan.end_time:
        logger.info(f"存储到数据库 - end_time: {plan.end_time} (类型: {type(plan.end_time)})")

    db.add(plan)
    db.commit()
    db.refresh(plan)
    
    # 初始化下次执行时间
    service = ScheduledPublishService(db)
    await service.update_plan_next_execution(plan.id)

    # 生成任务
    tasks_created = await service.generate_tasks_from_config(plan.id, plan_data.site_configs)

    # 更新计划的任务统计
    plan.total_tasks = len(tasks_created)
    db.commit()

    # 将任务加入队列
    await service.add_tasks_to_queue([task.id for task in tasks_created])

    # 添加到调度器
    await service.add_plan_to_scheduler(plan.id)

    return plan


@router.put("/plans/{plan_id}", response_model=ScheduledPublishPlanResponse)
async def update_scheduled_plan(
    plan_id: int,
    plan_data: ScheduledPublishPlanUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新定时发布计划"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")

    # 处理数据更新，特别处理 site_configs 字段
    update_data = plan_data.model_dump(exclude_unset=True)

    # 如果包含 site_configs，需要序列化为JSON字符串
    if 'site_configs' in update_data and update_data['site_configs'] is not None:
        site_configs = update_data['site_configs']
        if isinstance(site_configs, list):
            # 将 SiteConfigItem 对象列表序列化为JSON字符串
            update_data['site_configs'] = json.dumps([
                config.model_dump() if hasattr(config, 'model_dump') else config
                for config in site_configs
            ])

    # 更新字段
    for field, value in update_data.items():
        setattr(plan, field, value)

    db.commit()
    db.refresh(plan)

    # 更新调度器中的计划
    service = ScheduledPublishService(db)
    await service.update_plan_in_scheduler(plan_id)

    return plan


@router.delete("/plans/{plan_id}")
async def delete_scheduled_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除定时发布计划"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")

    # 从调度器中移除计划
    service = ScheduledPublishService(db)
    await service.remove_plan_from_scheduler(plan_id)

    db.delete(plan)
    db.commit()
    return {"message": "定时发布计划已删除"}


@router.get("/plans/{plan_id}/tasks", response_model=ScheduledPublishTaskList)
async def get_plan_tasks(
    plan_id: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取计划的任务列表"""
    query = db.query(ScheduledPublishTask).filter(ScheduledPublishTask.plan_id == plan_id)
    
    if status:
        query = query.filter(ScheduledPublishTask.status == status)
    
    total = query.count()
    offset = (page - 1) * size
    items = query.order_by(ScheduledPublishTask.created_at.desc()).offset(offset).limit(size).all()
    
    return ScheduledPublishTaskList(
        items=items,
        total=total,
        page=page,
        size=size
    )


@router.post("/generate-batch-tasks")
async def generate_batch_tasks(
    request: BatchTaskGenerateRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量生成定时发布任务"""
    
    # 创建定时发布计划
    plan_data = {
        "plan_name": request.plan_name,
        "keywords": request.custom_keywords,
        "keyword_categories": request.keyword_categories,
        "keyword_selection_strategy": request.keyword_selection_strategy,

        # 智能选词策略配置
        "use_keyword_strategy": request.use_keyword_strategy,
        "strategy_intent": request.strategy_intent,
        "strategy_volume_min": request.strategy_volume_min,
        "strategy_volume_max": request.strategy_volume_max,
        "strategy_difficulty_min": request.strategy_difficulty_min,
        "strategy_difficulty_max": request.strategy_difficulty_max,
        "strategy_cpc_min": request.strategy_cpc_min,
        "strategy_cpc_max": request.strategy_cpc_max,
        "strategy_competitive_density_min": request.strategy_competitive_density_min,
        "strategy_competitive_density_max": request.strategy_competitive_density_max,
        "strategy_countries": request.strategy_countries,
        "strategy_categories": request.strategy_categories,

        "site_categories": request.site_categories,
        "selected_sites": request.selected_sites,
        "site_configs": json.dumps([config.model_dump() for config in request.site_configs]) if request.site_configs else None,
        "default_blog_tags": ",".join(map(str, request.default_blog_tags)) if request.default_blog_tags else None,
        "ai_model": request.ai_model,
        "ai_config_id": request.ai_config_id,
        "scheduled_time": request.scheduled_time,
        "end_time": request.end_time,
        "frequency_type": request.frequency_type,
        "weekly_days": request.weekly_days,
        "custom_interval_value": request.custom_interval_value,
        "custom_interval_unit": request.custom_interval_unit,
        "daily_time": request.daily_time,
        "max_executions": request.max_executions
    }
    
    plan = ScheduledPublishPlan(**plan_data)
    db.add(plan)
    db.commit()
    db.refresh(plan)
    
    # 初始化下次执行时间
    service = ScheduledPublishService(db)
    await service.update_plan_next_execution(plan.id)
    
    # 生成任务，但设置scheduled_execution_time
    tasks_created = await service.generate_tasks_from_config(plan.id, request.site_configs)
    
    # 为每个任务设置计划执行时间
    for task in tasks_created:
        task.scheduled_execution_time = plan.next_execution_time
    
    # 更新计划的任务统计
    plan.total_tasks = len(tasks_created)
    db.commit()

    # 将计划添加到调度器中
    logger.info(f"将计划 {plan.id} 添加到调度器，下次执行时间: {plan.next_execution_time}")
    await service.add_plan_to_scheduler(plan.id)

    # 不要立即加入队列，等待定时调度器触发
    # background_tasks.add_task(service.add_tasks_to_queue, [task.id for task in tasks_created])

    return {
        "message": f"成功创建 {len(tasks_created)} 个定时发布任务，将在 {plan.next_execution_time} 执行",
        "plan_id": plan.id,
        "tasks_created": len(tasks_created),
        "next_execution_time": plan.next_execution_time
    }


@router.get("/test/data-check")
async def test_data_check(db: Session = Depends(get_db)):
    """测试数据检查（无认证，仅用于调试）"""
    try:
        # 检查关键词分类
        keyword_categories = db.query(KeywordLibrary.category).distinct().filter(
            KeywordLibrary.category.isnot(None)
        ).all()
        keyword_cats = [category[0] for category in keyword_categories if category[0]]
        
        # 检查站点分类
        site_categories = db.query(WordPressSite.category).distinct().filter(
            WordPressSite.category.isnot(None),
            WordPressSite.is_active == True
        ).all()
        site_cats = [category[0] for category in site_categories if category[0]]
        
        return {
            "status": "success",
            "keyword_categories": keyword_cats,
            "site_categories": site_cats,
            "keyword_count": len(keyword_cats),
            "site_count": len(site_cats)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@router.get("/keyword-categories")
async def get_keyword_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取关键词库分类列表"""
    categories = db.query(KeywordLibrary.category).distinct().filter(
        KeywordLibrary.category.isnot(None)
    ).all()
    
    return [category[0] for category in categories if category[0]]


@router.get("/site-categories")
async def get_site_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取站点分类列表"""
    categories = db.query(WordPressSite.category).distinct().filter(
        WordPressSite.category.isnot(None),
        WordPressSite.is_active == True
    ).all()
    
    return [category[0] for category in categories if category[0]]


@router.get("/sites-by-category")
async def get_sites_by_category(
    categories: str = Query(..., description="分类列表，逗号分隔"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """根据分类获取站点列表"""
    category_list = [cat.strip() for cat in categories.split(",")]
    
    sites = db.query(WordPressSite).filter(
        WordPressSite.category.in_(category_list),
        WordPressSite.is_active == True
    ).all()
    
    return [
        {
            "id": site.id,
            "name": site.name,
            "url": site.url,
            "category": site.category,
            "blog_categories": site.blog_categories or []
        }
        for site in sites
    ]


@router.get("/keywords-by-category")
async def get_keywords_by_category(
    categories: str = Query(..., description="分类列表，逗号分隔"),
    limit: int = Query(100, description="每个分类最多返回的关键词数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """根据分类获取关键词列表"""
    category_list = [cat.strip() for cat in categories.split(",")]
    
    keywords_by_category = {}
    
    for category in category_list:
        keywords = db.query(KeywordLibrary.keyword_name).filter(
            KeywordLibrary.category == category
        ).limit(limit).all()
        
        keywords_by_category[category] = [kw[0] for kw in keywords]
    
    return keywords_by_category


@router.post("/preview-strategy-keywords")
async def preview_strategy_keywords(
    strategy_data: dict,
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """预览选词策略筛选结果"""
    from ....models.keyword_library import KeywordLibrary
    from sqlalchemy import or_, and_

    # 构建查询条件
    query = db.query(KeywordLibrary).filter(
        ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
    )

    # 意图筛选
    if strategy_data.get('strategy_intent'):
        query = query.filter(KeywordLibrary.intent.contains(strategy_data['strategy_intent']))

    # 搜索量范围筛选
    if strategy_data.get('strategy_volume_min') is not None:
        query = query.filter(KeywordLibrary.volume >= strategy_data['strategy_volume_min'])
    if strategy_data.get('strategy_volume_max') is not None:
        query = query.filter(KeywordLibrary.volume <= strategy_data['strategy_volume_max'])

    # 难度范围筛选
    if strategy_data.get('strategy_difficulty_min') is not None:
        query = query.filter(KeywordLibrary.keyword_difficulty >= strategy_data['strategy_difficulty_min'])
    if strategy_data.get('strategy_difficulty_max') is not None:
        query = query.filter(KeywordLibrary.keyword_difficulty <= strategy_data['strategy_difficulty_max'])

    # CPC范围筛选
    if strategy_data.get('strategy_cpc_min') is not None:
        query = query.filter(KeywordLibrary.cpc_usd >= strategy_data['strategy_cpc_min'])
    if strategy_data.get('strategy_cpc_max') is not None:
        query = query.filter(KeywordLibrary.cpc_usd <= strategy_data['strategy_cpc_max'])

    # 竞争密度范围筛选
    if strategy_data.get('strategy_competitive_density_min') is not None:
        query = query.filter(KeywordLibrary.competitive_density >= strategy_data['strategy_competitive_density_min'])
    if strategy_data.get('strategy_competitive_density_max') is not None:
        query = query.filter(KeywordLibrary.competitive_density <= strategy_data['strategy_competitive_density_max'])

    # 国家筛选
    if strategy_data.get('strategy_countries'):
        countries = strategy_data['strategy_countries']
        if countries:
            country_conditions = []
            for country in countries:
                # 支持多种匹配方式
                if country.lower() == 'global':
                    # 全球：匹配空值、空字符串或包含global/全球的记录
                    country_conditions.append(
                        or_(
                            KeywordLibrary.location_ids.is_(None),     # 空值
                            KeywordLibrary.location_ids == '',         # 空字符串
                            KeywordLibrary.location_ids.ilike('%global%'),  # 包含global
                            KeywordLibrary.location_ids.ilike('%全球%')      # 包含全球
                        )
                    )
                else:
                    # 其他国家使用contains匹配，同时排除空值
                    country_conditions.append(
                        and_(
                            KeywordLibrary.location_ids.is_not(None),
                            KeywordLibrary.location_ids != '',
                            KeywordLibrary.location_ids.contains(country)
                        )
                    )
            if country_conditions:
                query = query.filter(or_(*country_conditions))

    # 趋势方向筛选
    if strategy_data.get('strategy_trend_direction'):
        trend_direction = strategy_data['strategy_trend_direction']
        if trend_direction and trend_direction != '':
            # 趋势数据通常是JSON格式的数组，需要解析后判断趋势方向
            if trend_direction == 'up':
                # 上升趋势：最后一个值大于第一个值
                query = query.filter(
                    and_(
                        KeywordLibrary.trend.is_not(None),
                        KeywordLibrary.trend != '',
                        # 这里可以添加更复杂的JSON解析逻辑，暂时先过滤有趋势数据的
                        KeywordLibrary.trend.contains('[')  # 简单判断是否为JSON数组格式
                    )
                )
            elif trend_direction == 'down':
                # 下降趋势
                query = query.filter(
                    and_(
                        KeywordLibrary.trend.is_not(None),
                        KeywordLibrary.trend != '',
                        KeywordLibrary.trend.contains('[')
                    )
                )
            elif trend_direction == 'stable':
                # 平稳趋势
                query = query.filter(
                    and_(
                        KeywordLibrary.trend.is_not(None),
                        KeywordLibrary.trend != '',
                        KeywordLibrary.trend.contains('[')
                    )
                )

    # 搜索结果数范围筛选
    if strategy_data.get('strategy_results_min') is not None:
        query = query.filter(KeywordLibrary.number_of_results >= strategy_data['strategy_results_min'])
    if strategy_data.get('strategy_results_max') is not None:
        query = query.filter(KeywordLibrary.number_of_results <= strategy_data['strategy_results_max'])

    # 分类筛选
    if strategy_data.get('strategy_categories'):
        categories = strategy_data['strategy_categories']
        if categories:
            query = query.filter(KeywordLibrary.category.in_(categories))

    # 获取总数（限制在合理范围内）
    count_query = query.limit(10000)  # 最多计算10000条
    total_count = count_query.count()

    # 计算分页偏移量
    offset = (page - 1) * page_size

    # 获取分页结果
    keywords = query.offset(offset).limit(page_size).all()

    return {
        "total_count": total_count,
        "preview_keywords": [
            {
                "keyword_name": kw.keyword_name,
                "intent": kw.intent,
                "volume": kw.volume,
                "trend": kw.trend,  # 添加趋势字段
                "keyword_difficulty": kw.keyword_difficulty,
                "cpc_usd": float(kw.cpc_usd) if kw.cpc_usd else None,
                "competitive_density": float(kw.competitive_density) if kw.competitive_density else None,
                "number_of_results": kw.number_of_results,  # 添加搜索结果数字段
                "category": kw.category,
                "location_ids": kw.location_ids
            }
            for kw in keywords
        ]
    }


@router.get("/check-database-schema")
async def check_database_schema(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """检查数据库字段是否存在"""
    try:
        # 检查计划表的keyword_selection_strategy字段
        plan_has_strategy = False
        try:
            result = db.execute("SELECT keyword_selection_strategy FROM scheduled_publish_plans LIMIT 1")
            plan_has_strategy = True
        except Exception:
            plan_has_strategy = False

        # 检查任务表的actual_keyword字段
        task_has_actual_keyword = False
        try:
            result = db.execute("SELECT actual_keyword FROM scheduled_publish_tasks LIMIT 1")
            task_has_actual_keyword = True
        except Exception:
            task_has_actual_keyword = False

        return {
            "plan_has_keyword_selection_strategy": plan_has_strategy,
            "task_has_actual_keyword": task_has_actual_keyword,
            "migration_needed": not (plan_has_strategy and task_has_actual_keyword)
        }
    except Exception as e:
        return {
            "error": str(e),
            "plan_has_keyword_selection_strategy": False,
            "task_has_actual_keyword": False,
            "migration_needed": True
        }


@router.get("/queue-status", response_model=QueueStatusResponse)
async def get_queue_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务队列状态"""
    service = ScheduledPublishService(db)
    return await service.get_queue_status()


@router.post("/start-queue-worker")
async def start_queue_worker(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动队列工作者"""
    service = ScheduledPublishService(db)
    background_tasks.add_task(service.start_queue_worker)
    
    return {"message": "队列工作者已启动"}


@router.get("/statistics", response_model=List[PlanStatistics])
async def get_plan_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有计划的统计信息"""
    service = ScheduledPublishService(db)
    return await service.get_plan_statistics()


@router.post("/execute-plan/{plan_id}")
async def execute_plan_immediately(
    plan_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """立即执行指定计划"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")
    
    if not plan.is_active:
        raise HTTPException(status_code=400, detail="计划已禁用，无法执行")
    
    service = ScheduledPublishService(db)
    
    # 检查是否有pending状态的任务
    pending_tasks = db.query(ScheduledPublishTask).filter(
        ScheduledPublishTask.plan_id == plan_id,
        ScheduledPublishTask.status == "pending"
    ).all()
    
    if pending_tasks:
        # 如果有pending任务，直接执行
        task_ids = [task.id for task in pending_tasks]
        await service.add_tasks_to_queue(task_ids)
        return {"message": f"计划 {plan.plan_name} 的 {len(task_ids)} 个待执行任务已加入执行队列"}
    else:
        # 如果没有pending任务，使用统一的任务生成逻辑（支持保存的站点配置）
        service = ScheduledPublishService(db)

        # 使用统一的任务生成函数，它会优先使用保存的站点配置
        from datetime import datetime
        current_time = datetime.utcnow()

        # 调用统一的任务生成函数
        task_ids = await service._generate_new_tasks_for_plan(plan, db, current_time)

        if task_ids:
            # 立即加入队列执行
            await service.add_tasks_to_queue(task_ids)

            # 更新计划统计
            plan.total_tasks += len(task_ids)
            db.commit()

            return {"message": f"计划 {plan.plan_name} 已根据保存的配置重新生成 {len(task_ids)} 个任务并加入执行队列"}
        else:
            return {"message": f"计划 {plan.plan_name} 没有可用的站点配置，无法生成任务"}


@router.post("/tasks/check-status")
async def check_tasks_status(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """检查所有运行中的任务状态"""
    service = ScheduledPublishService(db)
    background_tasks.add_task(service.check_running_tasks)
    return {"message": "任务状态检查已启动"}


@router.post("/plans/{plan_id}/refresh-statistics")
async def refresh_plan_statistics(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刷新计划统计信息"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")
    
    service = ScheduledPublishService(db)
    service._update_plan_statistics(plan)
    db.commit()
    
    return {"message": "计划统计信息已刷新"}


@router.post("/refresh-all-statistics")
async def refresh_all_plan_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刷新所有计划的统计信息"""
    plans = db.query(ScheduledPublishPlan).all()
    service = ScheduledPublishService(db)
    
    for plan in plans:
        service._update_plan_statistics(plan)
    
    db.commit()
    
    return {"message": f"已刷新 {len(plans)} 个计划的统计信息"}


@router.post("/cleanup-queue")
async def cleanup_completed_queue(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """清理已完成的队列项"""
    service = ScheduledPublishService(db)
    await service.cleanup_completed_queue_items()
    return {"message": "队列清理完成"}


@router.post("/tasks/update-queue-status")
async def update_queue_status(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新队列中所有任务的状态"""
    service = ScheduledPublishService(db)
    
    # 获取所有队列中的任务
    queue_items = db.query(TaskQueue).filter(TaskQueue.status == "running").all()
    
    for queue_item in queue_items:
        background_tasks.add_task(service.update_task_status, queue_item.task_id)
    
    return {"message": f"已开始更新 {len(queue_items)} 个队列任务的状态"}


@router.get("/plans/{plan_id}/debug")
async def debug_plan_frequency(
    plan_id: int,
    db: Session = Depends(get_db)
):
    """调试计划频率配置（无认证，仅用于调试）"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        return {"error": "计划不存在"}
    
    return {
        "plan_id": plan.id,
        "plan_name": plan.plan_name,
        "frequency_type": plan.frequency_type,
        "scheduled_time": plan.scheduled_time,
        "end_time": plan.end_time,
        "daily_time": plan.daily_time,
        "weekly_days": plan.weekly_days,
        "custom_interval_value": plan.custom_interval_value,
        "custom_interval_unit": plan.custom_interval_unit,
        "max_executions": plan.max_executions,
        "current_executions": plan.current_executions,
        "next_execution_time": plan.next_execution_time,
        "is_active": plan.is_active
    }


@router.post("/start-scheduler")
async def start_scheduler(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动定时任务调度器"""
    service = ScheduledPublishService(db)
    background_tasks.add_task(service.start_scheduler)

    return {"message": "定时任务调度器已启动"}


@router.get("/scheduler-status")
async def get_scheduler_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取调度器状态"""
    service = ScheduledPublishService(db)
    return service.get_scheduler_status()


@router.get("/events/queue-status")
async def queue_status_events(
    token: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """队列状态SSE事件流"""
    # 通过token验证用户
    if not token:
        raise HTTPException(status_code=401, detail="需要认证token")

    try:
        from jose import jwt, JWTError
        from ....core.config import settings
        from ....schemas.token import TokenPayload

        # 解码JWT token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        token_data = TokenPayload(**payload)

        # 查找用户
        user = db.query(User).filter(User.id == token_data.sub).first()
        if not user:
            raise HTTPException(status_code=401, detail="用户不存在")
        if not user.is_active:
            raise HTTPException(status_code=401, detail="用户已禁用")

    except (JWTError, Exception) as e:
        raise HTTPException(status_code=401, detail="认证失败")
    from ....services.event_manager import event_manager, queue_status_event_generator

    # 创建事件队列
    queue = asyncio.Queue()

    # 添加到事件管理器
    event_manager.add_connection("queue_status", queue)

    try:
        # 返回SSE流
        return StreamingResponse(
            queue_status_event_generator(queue),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    except Exception as e:
        # 清理连接
        event_manager.remove_connection("queue_status", queue)
        raise e


@router.get("/events/plan-list")
async def plan_list_events(
    token: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """计划列表SSE事件流"""
    # 通过token验证用户
    if not token:
        raise HTTPException(status_code=401, detail="需要认证token")

    try:
        from jose import jwt, JWTError
        from ....core.config import settings
        from ....schemas.token import TokenPayload

        # 解码JWT token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        token_data = TokenPayload(**payload)

        # 查找用户
        user = db.query(User).filter(User.id == token_data.sub).first()
        if not user:
            raise HTTPException(status_code=401, detail="用户不存在")
        if not user.is_active:
            raise HTTPException(status_code=401, detail="用户已禁用")

    except (JWTError, Exception) as e:
        raise HTTPException(status_code=401, detail="认证失败")
    from ....services.event_manager import event_manager, queue_status_event_generator

    # 创建事件队列
    queue = asyncio.Queue()

    # 添加到事件管理器
    event_manager.add_connection("plan_list", queue)

    try:
        # 返回SSE流
        return StreamingResponse(
            queue_status_event_generator(queue),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    except Exception as e:
        # 清理连接
        event_manager.remove_connection("plan_list", queue)
        raise e


@router.get("/events/task-status")
async def task_status_events(
    token: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """任务状态SSE事件流"""
    # 通过token验证用户
    if not token:
        raise HTTPException(status_code=401, detail="需要认证token")

    try:
        from jose import jwt, JWTError
        from ....core.config import settings
        from ....schemas.token import TokenPayload

        # 解码JWT token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        token_data = TokenPayload(**payload)

        # 查找用户
        user = db.query(User).filter(User.id == token_data.sub).first()
        if not user:
            raise HTTPException(status_code=401, detail="用户不存在")
        if not user.is_active:
            raise HTTPException(status_code=401, detail="用户已禁用")

    except (JWTError, Exception) as e:
        raise HTTPException(status_code=401, detail="认证失败")
    from ....services.event_manager import event_manager, queue_status_event_generator

    # 创建事件队列
    queue = asyncio.Queue()

    # 添加到事件管理器
    event_manager.add_connection("task_status", queue)

    try:
        # 返回SSE流
        return StreamingResponse(
            queue_status_event_generator(queue),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    except Exception as e:
        # 清理连接
        event_manager.remove_connection("task_status", queue)
        raise e


@router.post("/test/trigger-events")
async def test_trigger_events():
    """测试事件触发机制（无认证）"""
    from ....services.event_manager import trigger_queue_status_update, trigger_plan_list_update, trigger_task_status_update

    # 触发测试事件
    trigger_queue_status_update({
        "total_queued": 3,
        "running_tasks": 1,
        "available_workers": 4,
        "estimated_wait_time": 120
    })

    trigger_plan_list_update({
        "plan_id": 41,
        "action": "test_update",
        "message": "这是一个测试事件"
    })

    trigger_task_status_update({
        "task_id": 999,
        "status": "success",
        "plan_id": 41,
        "completion_time": "2025-06-07T16:35:00Z",
        "error_message": None
    })

    return {"message": "测试事件已触发", "status": "success"}


@router.get("/check-updates")
async def check_updates(db: Session = Depends(get_db)):
    """检查是否有数据更新"""
    try:
        # 简单的更新检测机制：检查最近是否有任务完成
        from datetime import datetime, timedelta

        # 检查最近1分钟内是否有任务完成
        one_minute_ago = datetime.utcnow() - timedelta(minutes=1)
        recent_completed_tasks = db.query(ScheduledPublishTask).filter(
            ScheduledPublishTask.status == 'success',
            ScheduledPublishTask.completion_time >= one_minute_ago
        ).count()

        should_refresh = recent_completed_tasks > 0

        return {
            "should_refresh": should_refresh,
            "recent_completed_tasks": recent_completed_tasks,
            "check_time": datetime.utcnow().isoformat()
        }
    except Exception as e:
        # 静默处理错误，返回不需要刷新
        return {
            "should_refresh": False,
            "error": str(e),
            "check_time": datetime.utcnow().isoformat()
        }