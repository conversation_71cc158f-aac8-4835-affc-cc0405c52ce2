-- 回滚 execution_mode 字段的添加
-- 执行日期: 2024-01-15
-- 描述: 移除 scheduled_publish_plans 表中的 execution_mode 字段

-- 1. 备份现有数据（可选）
-- CREATE TABLE scheduled_publish_plans_backup AS 
-- SELECT * FROM scheduled_publish_plans WHERE execution_mode IS NOT NULL;

-- 2. 删除 execution_mode 字段
ALTER TABLE scheduled_publish_plans 
DROP COLUMN execution_mode;

-- 3. 验证字段已删除
DESCRIBE scheduled_publish_plans;

-- 4. 确认字段不存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'scheduled_publish_plans' 
    AND COLUMN_NAME = 'execution_mode';
